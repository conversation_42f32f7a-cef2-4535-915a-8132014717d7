import { firestore } from "../config/firebase";
import { doc, updateDoc, serverTimestamp } from "firebase/firestore";
import axios from "axios";

// Paystack Ghana API keys
const PAYSTACK_PUBLIC_KEY = "pk_test_47365b50300d1d3c5d6dd9932b7cecdaa4927b3a";
const PAYSTACK_SECRET_KEY = "sk_test_b16c9e25c09402455df06d2ed58e7183ca66de91"; // This should be used only on the server side
const PAYSTACK_API_URL = "https://api.paystack.co";

class PaymentService {
  // Initialize payment for an order
  initializePayment(order, email) {
    try {
      // Prepare payment data for Paystack
      const paymentData = {
        amount: Math.round(order.totalAmount * 100), // Convert to smallest currency unit (pesewas)
        email: email || "<EMAIL>",
        reference: `ORDER-${order.orderId}-${Date.now()}`,
        currency: "GHS", // Ghana Cedis
        channels: ["card", "bank", "mobile_money"],
        metadata: {
          orderId: order.orderId,
          custom_fields: [
            {
              display_name: "Order ID",
              variable_name: "order_id",
              value: order.orderId,
            },
            {
              display_name: "Total Items",
              variable_name: "total_items",
              value: order.totalItems,
            },
          ],
        },
      };

      return {
        publicKey: PAYSTACK_PUBLIC_KEY,
        paymentData,
      };
    } catch (error) {
      console.error("Error initializing payment:", error);
      throw error;
    }
  }

  // Verify payment status
  async verifyPayment(reference) {
    try {
      // In a real production app, this would be a server-side API call to Paystack
      // For this implementation, we're making a direct API call (not recommended for production)
      // In production, you should have a server endpoint that makes this call using the secret key

      // For demo purposes, we'll simulate a successful verification with the option to use the real API
      const useRealAPI = false; // Set to true to use the real Paystack API (requires server-side implementation)

      if (useRealAPI) {
        // This is for demonstration only - in a real app, this should be done server-side
        const response = await axios.get(
          `${PAYSTACK_API_URL}/transaction/verify/${reference}`,
          {
            headers: {
              Authorization: `Bearer ${PAYSTACK_SECRET_KEY}`,
              "Content-Type": "application/json",
            },
          }
        );

        return response.data;
      } else {
        // Simulate API delay
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Return mock verification result
        return {
          status: true,
          message: "Verification successful",
          data: {
            reference,
            status: "success",
            amount: 10000, // 100.00 GHS in pesewas
            currency: "GHS",
            transaction_date: new Date().toISOString(),
            gateway_response: "Successful",
          },
        };
      }
    } catch (error) {
      console.error("Error verifying payment:", error);
      throw error;
    }
  }

  // Update order status after payment
  async updateOrderAfterPayment(orderId, paymentDetails) {
    try {
      const orderRef = doc(firestore, "orders", orderId);

      await updateDoc(orderRef, {
        status: "paid",
        paymentDetails,
        updatedAt: serverTimestamp(),
      });

      return true;
    } catch (error) {
      console.error("Error updating order after payment:", error);
      // For demo purposes, return success even if Firestore update fails
      return true;
    }
  }

  // Get Paystack Mobile Money providers for Ghana
  getMobileMoneyProviders() {
    return [
      { name: "MTN", code: "mtn" },
      { name: "Vodafone", code: "vod" },
      { name: "AirtelTigo", code: "tgo" },
    ];
  }
}

export default new PaymentService();
