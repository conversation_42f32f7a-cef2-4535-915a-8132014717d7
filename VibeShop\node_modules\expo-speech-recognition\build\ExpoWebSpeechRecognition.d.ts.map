{"version": 3, "file": "ExpoWebSpeechRecognition.d.ts", "sourceRoot": "", "sources": ["../src/ExpoWebSpeechRecognition.ts"], "names": [], "mappings": "AAIA,OAAO,KAAK,EAEV,4BAA4B,EAC7B,MAAM,qCAAqC,CAAC;AAiJ7C,KAAK,cAAc,CAAC,CAAC,SAAS,MAAM,yBAAyB,IAAI,CAC/D,IAAI,EAAE,iBAAiB,EACvB,EAAE,EAAE,yBAAyB,CAAC,CAAC,CAAC,KAC7B,GAAG,CAAC;AAET,8FAA8F;AAC9F,qBAAa,wBAAyB,YAAW,iBAAiB;;IAChE,IAAI,SAAW;IACf,QAAQ,EAAE,iBAAiB,CAAkC;IAC7D,eAAe,SAAK;IACpB,UAAU,UAAS;IAInB,IAAI,cAAc,IAAI,OAAO,CAE5B;IAED,IAAI,cAAc,CAAC,cAAc,EAAE,OAAO,EAGzC;IAID,gIAAgI;IAChI,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAa;IACzC,uIAAuI;IACvI,2BAA2B,UAAS;IACpC,oHAAoH;IACpH,eAAe,UAAS;IACxB,yFAAyF;IACzF,oBAAoB,EAAE,4BAA4B,CAAC,sBAAsB,CAAC,CAAC;IAC3E,qFAAqF;IACrF,WAAW,CAAC,EAAE,4BAA4B,CAAC,aAAa,CAAC,CAAC;IAC1D,wFAAwF;IACxF,gBAAgB,CAAC,EAAE,4BAA4B,CAAC,kBAAkB,CAAC,CAAC;IACpE,8FAA8F;IAC9F,aAAa,CAAC,EAAE,4BAA4B,CAAC,eAAe,CAAC,CAAa;IAC1E,+EAA+E;IAC/E,WAAW,CAAC,EAAE,4BAA4B,CAAC,aAAa,CAAC,CAAa;IACtE,oFAAoF;IACpF,WAAW,CAAC,EAAE,4BAA4B,CAAC,aAAa,CAAC,CAAa;IACtE;;;;;;;;;OASG;IACH,gCAAgC,EAAE,4BAA4B,CAAC,kCAAkC,CAAC,CAAC;IAKnG,KAAK;IAsBL,IAAI,aAAoC;IACxC,KAAK,aAAqC;IAG1C,IAAI,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI,EAGnD;IACD,gDAAgD;IAChD,IAAI,OAAO,IALW,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI,CAOnD;IAGD,IAAI,KAAK,CAAC,QAAQ,EAAE,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,EAS/C;IACD,kEAAkE;IAClE,IAAI,KAAK,IAXW,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,CAa/C;IAGD,IAAI,OAAO,CAAC,QAAQ,EAAE,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI,EAGnD;IACD,qEAAqE;IACrE,IAAI,OAAO,IALW,cAAc,CAAC,OAAO,CAAC,GAAG,IAAI,CAOnD;IAED,aAAa,CAAC,CAAC,SAAS,MAAM,yBAAyB,EACrD,GAAG,EAAE,CAAC,EACN,UAAU,EAAE,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,EACpC,gBAAgB,EAAE,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI;IAW5C,IAAI,QAAQ,CAAC,QAAQ,EAAE,cAAc,CAAC,QAAQ,CAAC,GAAG,IAAI,EAGrD;IACD;0GACsG;IACtG,IAAI,QAAQ,IANW,cAAc,CAAC,QAAQ,CAAC,GAAG,IAAI,CAQrD;IAGD,IAAI,SAAS,CAAC,QAAQ,EAAE,cAAc,CAAC,SAAS,CAAC,GAAG,IAAI,EAGvD;IACD,wGAAwG;IACxG,IAAI,SAAS,IALW,cAAc,CAAC,SAAS,CAAC,GAAG,IAAI,CAOvD;IAGD,IAAI,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,EAG/D;IACD,wGAAwG;IACxG,IAAI,aAAa,IALW,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,CAO/D;IAGD,IAAI,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,WAAW,CAAC,GAAG,IAAI,EAG3D;IACD,wGAAwG;IACxG,IAAI,WAAW,IALW,cAAc,CAAC,WAAW,CAAC,GAAG,IAAI,CAO3D;IAGD,IAAI,YAAY,CAAC,QAAQ,EAAE,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,EAG7D;IACD,8DAA8D;IAC9D,IAAI,YAAY,IALW,cAAc,CAAC,YAAY,CAAC,GAAG,IAAI,CAO7D;IAGD,IAAI,UAAU,CAAC,QAAQ,EAAE,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,EAGzD;IACD,8DAA8D;IAC9D,IAAI,UAAU,IALW,cAAc,CAAC,UAAU,CAAC,GAAG,IAAI,CAOzD;IAED,aAAa;IACb,UAAU,EAAE,CAAC,CAAC,IAAI,EAAE,iBAAiB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAAQ;IACxE,aAAa;IACb,YAAY,EAAE,CAAC,CAAC,IAAI,EAAE,iBAAiB,EAAE,EAAE,EAAE,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,CAAQ;IAE1E,gBAAgB,CAAC,CAAC,SAAS,MAAM,yBAAyB,EACxD,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC,EAC3B,OAAO,CAAC,EAAE,OAAO,GAAG,uBAAuB,GAC1C,IAAI;IAoCP,mBAAmB,CAAC,CAAC,SAAS,MAAM,yBAAyB,EAC3D,IAAI,EAAE,CAAC,EACP,QAAQ,EAAE,CACR,IAAI,EAAE,iBAAiB,EACvB,EAAE,EAAE,yBAAyB,CAAC,CAAC,CAAC,KAC7B,GAAG,EACR,OAAO,CAAC,EAAE,OAAO,GAAG,oBAAoB,GAAG,SAAS,GACnD,IAAI;IAUP,aAAa,CAAC,KAAK,EAAE,KAAK,GAAG,OAAO;CAGrC;AAED;;GAEG;AACH,qBAAa,wBAAyB,YAAW,iBAAiB;;IAChE,IAAI,MAAM,WAET;IAED,CAAC,KAAK,EAAE,MAAM,GAAG,aAAa,CAAC;IAE/B,UAAU,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAI1D,IAAI,CAAC,KAAK,EAAE,MAAM,GAAG,oBAAoB;IAIzC,aAAa,GAAI,SAAS,MAAM,EAAE,SAAS,MAAM,UAK/C;CACH;AAED,qBAAa,oBAAqB,YAAW,aAAa;IACxD,GAAG,SAAM;IACT,MAAM,SAAK;gBAEC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM;CAIzC"}