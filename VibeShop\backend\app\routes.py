import os
import tempfile
from flask import Blueprint, request, jsonify
import speech_recognition as sr
from pydub import AudioSegment
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

main_bp = Blueprint('main', __name__)

@main_bp.route('/health', methods=['GET'])
def health_check():
    """Simple health check endpoint."""
    return jsonify({'status': 'ok', 'message': 'Voice recognition service is running'})

@main_bp.route('/recognize', methods=['POST'])
def recognize_speech():
    """
    Endpoint to recognize speech from audio file.
    Expects audio file in the request.
    Returns the recognized text.
    """
    logger.info("Received speech recognition request")
    
    if 'audio' not in request.files:
        logger.error("No audio file in request")
        return jsonify({'error': 'No audio file provided'}), 400
    
    audio_file = request.files['audio']
    
    if audio_file.filename == '':
        logger.error("Empty filename")
        return jsonify({'error': 'No selected file'}), 400
    
    # Get language from request (default to English)
    language = request.form.get('language', 'en-US')
    
    # Check if advanced recognition is requested
    use_advanced = request.form.get('advanced', 'false').lower() == 'true'
    
    try:
        # Save the uploaded file to a temporary location
        temp_dir = tempfile.mkdtemp()
        temp_path = os.path.join(temp_dir, 'temp_audio')
        audio_file.save(temp_path)
        
        logger.info(f"Saved audio file to {temp_path}")
        
        # Convert audio to format suitable for recognition if needed
        # Most mobile devices record in m4a format, which needs conversion
        if audio_file.filename.endswith('.m4a'):
            logger.info("Converting m4a to wav")
            audio = AudioSegment.from_file(temp_path, format="m4a")
            wav_path = os.path.join(temp_dir, 'temp_audio.wav')
            audio.export(wav_path, format="wav")
            audio_path = wav_path
        else:
            audio_path = temp_path
        
        # Use advanced recognition if requested
        if use_advanced:
            try:
                from .torch_speech import get_recognizer
                logger.info("Using advanced speech recognition with PyTorch")
                
                recognizer = get_recognizer()
                text = recognizer.recognize(audio_path)
                
                # Preprocess the recognized text
                from .speech_utils import preprocess_command
                text = preprocess_command(text)
                
                logger.info(f"Advanced recognition result: {text}")
            except ImportError as e:
                logger.warning(f"Advanced recognition not available: {e}")
                logger.info("Falling back to standard recognition")
                use_advanced = False
        
        # Use standard recognition if advanced is not requested or failed
        if not use_advanced:
            # Initialize recognizer
            recognizer = sr.Recognizer()
            
            # Load audio file
            with sr.AudioFile(audio_path) as source:
                audio_data = recognizer.record(source)
                
                # Recognize speech using Google Speech Recognition
                logger.info(f"Recognizing speech with language {language}")
                text = recognizer.recognize_google(audio_data, language=language)
                
                # Preprocess the recognized text
                from .speech_utils import preprocess_command
                text = preprocess_command(text)
                
                logger.info(f"Recognized text: {text}")
        
        # Clean up temporary files
        try:
            os.remove(temp_path)
            if audio_file.filename.endswith('.m4a'):
                os.remove(wav_path)
            os.rmdir(temp_dir)
        except Exception as e:
            logger.warning(f"Error cleaning up temp files: {e}")
        
        return jsonify({
            'success': True,
            'text': text,
            'language': language,
            'advanced': use_advanced
        })
    
    except sr.UnknownValueError:
        logger.error("Speech Recognition could not understand audio")
        return jsonify({
            'success': False,
            'error': 'Could not understand audio',
            'text': ''
        })
    
    except sr.RequestError as e:
        logger.error(f"Could not request results from Speech Recognition service; {e}")
        return jsonify({
            'success': False,
            'error': f'Speech recognition service error: {e}',
            'text': ''
        })
    
    except Exception as e:
        logger.error(f"Error processing audio: {e}")
        return jsonify({
            'success': False,
            'error': f'Error processing audio: {str(e)}',
            'text': ''
        })

@main_bp.route('/commands', methods=['GET'])
def get_available_commands():
    """Return a list of available voice commands."""
    commands = [
        {"command": "search for [product]", "description": "Search for products"},
        {"command": "select item [number]", "description": "Select a product from the list"},
        {"command": "add to cart", "description": "Add the current product to your cart"},
        {"command": "view cart", "description": "Open your shopping cart"},
        {"command": "checkout", "description": "Proceed to checkout"},
        {"command": "go back", "description": "Return to the previous screen"},
        {"command": "go home", "description": "Return to the home screen"},
        {"command": "help", "description": "Show available commands"}
    ]
    
    return jsonify({'commands': commands}) 

@main_bp.route('/models', methods=['GET'])
def get_available_models():
    """Return information about available speech recognition models."""
    models = [
        {
            "id": "google",
            "name": "Google Speech Recognition",
            "description": "Standard cloud-based speech recognition",
            "default": True
        },
        {
            "id": "wav2vec2",
            "name": "Wav2Vec2",
            "description": "Advanced on-device speech recognition using PyTorch",
            "default": False
        }
    ]
    
    return jsonify({'models': models}) 