{"version": 3, "file": "ExpoSpeechRecognitionModule.types.js", "sourceRoot": "", "sources": ["../src/ExpoSpeechRecognitionModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { PermissionResponse } from \"expo-modules-core\";\nimport type { NativeModule } from \"expo\";\n\nexport type ExpoSpeechRecognitionPermissionResponse = PermissionResponse & {\n  /**\n   * Whether the speech recognition is restricted by Content & Privacy Restrictions.\n   *\n   * This value corresponds to the `restricted` enum of `SFSpeechRecognizer.authorizationStatus()`.\n   *\n   * This is only available on iOS.\n   */\n  restricted?: boolean;\n};\n\nimport type {\n  AudioEncodingAndroid,\n  AVAudioSessionCategory,\n  AVAudioSessionCategoryOptions,\n  AVAudioSessionMode,\n  RecognizerIntentEnableLanguageSwitch,\n  RecognizerIntentExtraLanguageModel,\n  TaskHintIOS,\n} from \"./constants\";\n\nexport type AVAudioSessionCategoryValue =\n  (typeof AVAudioSessionCategory)[keyof typeof AVAudioSessionCategory];\n\nexport type ExpoSpeechRecognitionResult = {\n  transcript: string;\n  /**\n   * Value ranging between between 0.0, 1.0, and -1 (unavailable) indicating transcript confidence.\n   */\n  confidence: number;\n  /**\n   * An array of transcription segments that represent the parts of the transcription, as identified by the speech recognizer.\n   *\n   * Notes for Android:\n   *\n   * - This is only available for SDK 34+ (Android 14+)\n   * - This is only verified to work with the `com.google.android.as` service (using on device speech recognition)\n   * - Segments are only available during the final result\n   * - The segment parts are split up by words.\n   * - The segments are only available for the first transcript\n   * - Segment confidences currently return as -1 (unavailable)\n   *\n   * Notes for iOS:\n   *\n   * - The confidence value will be 0 on partial results\n   */\n  segments: ExpoSpeechRecognitionResultSegment[];\n};\n\nexport type ExpoSpeechRecognitionResultSegment = {\n  /** The start timestamp of the utterance, e.g. 1000 */\n  startTimeMillis: number;\n  /** The end timestamp of the utterance, e.g. 1500 */\n  endTimeMillis: number;\n  /** The text portion of the transcript, e.g. \"Hello world\" */\n  segment: string;\n  /** Value ranging between between 0.0, 1.0, and -1 (unavailable) indicating the confidence of the specific segment */\n  confidence: number;\n};\n\n/** Fired when there's a speech result. The result may be partial or final */\nexport type ExpoSpeechRecognitionResultEvent = {\n  isFinal: boolean;\n  results: ExpoSpeechRecognitionResult[];\n};\n\nexport type ExpoSpeechRecognitionErrorCode =\n  /** The user called `ExpoSpeechRecognitionModule.abort()`. */\n  | \"aborted\"\n  /** Audio recording error. */\n  | \"audio-capture\"\n  /** There was an error in the speech recognition grammar or semantic tags, or the chosen grammar format or semantic tag format was unsupported. */\n  | \"bad-grammar\"\n  /** Locale is not supported by the speech recognizer. */\n  | \"language-not-supported\"\n  /** Network communication required for completing the recognition failed. */\n  | \"network\"\n  /** No final speech was detected. */\n  | \"no-speech\"\n  /** Permission to use speech recognition or microphone was not granted. */\n  | \"not-allowed\"\n  /** Recognizer is unavailable. */\n  | \"service-not-allowed\"\n  // Extra codes (not part of the spec)\n  /** The recognizer is busy and cannot accept any new recognition requests. */\n  | \"busy\"\n  /** (Android only) An unknown client-side error occurred. */\n  | \"client\"\n  /** (Android) No speech input. */\n  | \"speech-timeout\"\n  /** (Android) Unknown error */\n  | \"unknown\";\n\nexport type ExpoSpeechRecognitionErrorEvent = {\n  error: ExpoSpeechRecognitionErrorCode;\n  message: string;\n};\n\nexport type LanguageDetectionEvent = {\n  /** The language that was detected, in BCP-47 format. e.g. \"en-US\", \"de-DE\" */\n  detectedLanguage: string;\n  /** The confidence of the detected language. A value ranging between 0.0 and 1.0.\n   *\n   * Values range from:\n   *\n   * - 1.0 (highly confident)\n   * - 0.8 (confident)\n   * - 0.5 (not confident)\n   * - 0.0 (unknown)\n   */\n  confidence: number;\n  /** The alternative locales for the same language, in BCP-47 format. e.g. [\"en-US\", \"en-GB\"] */\n  topLocaleAlternatives: string[];\n};\n\n/**\n * Events that are dispatched from the native side\n */\nexport type ExpoSpeechRecognitionNativeEventMap = {\n  result: ExpoSpeechRecognitionResultEvent;\n  error: ExpoSpeechRecognitionErrorEvent;\n  start: null;\n  speechstart: null;\n  speechend: null;\n  /** A final result is returned with no significant recognition */\n  nomatch: null;\n  /** Audio capturing had started */\n  audiostart: {\n    /**\n     * The uri is set when `recordingOptions.persist` is enabled.\n     * Do not attempt to use this file until the `audioend` event is emitted.\n     *\n     * Example URIs:\n     *\n     * - Android: `file:///data/user/0/expo.modules.speechrecognition.example/cache/recording_1720678500903.wav`\n     * - iOS: `file:///path/to/Library/Caches/audio_CD5E6C6C-3D9D-4754-9188-D6FAF97D9DF2.caf`\n     */\n    uri: string | null;\n  };\n  /** Audio capturing had ended */\n  audioend: {\n    /**\n     * The uri is set when `recordingOptions.persist` is enabled.\n     *\n     * Example URIs:\n     *\n     * - Android: `file:///data/user/0/expo.modules.speechrecognition.example/cache/recording_1720678500903.wav`\n     * - iOS: `file:///path/to/Library/Caches/audio_CD5E6C6C-3D9D-4754-9188-D6FAF97D9DF2.caf`\n     */\n    uri: string | null;\n  };\n  end: null;\n  soundstart: null;\n  soundend: null;\n  languagedetection: LanguageDetectionEvent;\n  volumechange: {\n    /**\n     * A float value between -2 and 10 indicating the volume of the input audio\n     *\n     * Consider anything below 0 to be inaudible\n     */\n    value: number;\n  };\n};\n\nexport type ExpoSpeechRecognitionOptions = {\n  /** [Default: \"en-US\"] The language of the speech recognition */\n  lang?: string;\n  /** [Default: false] Note for iOS: final results are only available after speech recognition has stopped */\n  interimResults?: boolean;\n  /** [Default: 5] The maximum number of alternative transcriptions to return. */\n  maxAlternatives?: number;\n  /**\n   * An array of strings that will be used to provide context to the speech recognition engine.\n   *\n   * On Android, this configures [`EXTRA_BIASING_STRINGS`](https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_BIASING_STRINGS) in the recognizer intent (API level 33+).\n   *\n   * On iOS, this configures [`SFSpeechRecognitionRequest.contextualStrings`](https://developer.apple.com/documentation/speech/sfspeechrecognitionrequest/1649391-contextualstrings).\n   */\n  contextualStrings?: string[];\n  /**\n   * [Default: false] Continuous recognition.\n   *\n   * Not supported on Android 12 and below.\n   *\n   * If false, the behaviors are the following:\n   *\n   *   - on iOS 17-, recognition will run until no speech is detected for 3 seconds.\n   *   - on iOS 18+ and Android, recognition will run until a result with `isFinal: true` is received.\n   */\n  continuous?: boolean;\n  /** [Default: false] Prevent device from sending audio over the network. Only enabled if the device supports it.\n   *\n   * Use `getSupportedLocales()` to verify if the locale is installed on the device prior to enabling this option.\n   */\n  requiresOnDeviceRecognition?: boolean;\n  /**\n   * [Default: false] Include punctuation in the recognition results. This applies to full stops and commas.\n   *\n   * On Android, this configures [`EXTRA_ENABLE_FORMATTING`](https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_ENABLE_FORMATTING) in the recognizer intent (Android 13+, API level 33+).\n   *\n   * Note for Android: This feature is only verified to work on Android 13+ with on-device speech recognition enabled (i.e. enabling `requiresOnDeviceRecognition` or using the `com.google.android.as` service package)\n   *\n   * On iOS, this configures [`SFSpeechRecognitionRequest.addsPunctuation`](https://developer.apple.com/documentation/speech/sfspeechrecognitionrequest/3930023-addspunctuation).\n   */\n  addsPunctuation?: boolean;\n  /**\n   * The package name of the speech recognition service to use.\n   * If not provided, the default service will be used.\n   *\n   * Obtain the supported packages by running `ExpoSpeechRecognitionModule.getSpeechRecognitionServices()`\n   *\n   * e.g. \"com.google.android.as\" or \"com.samsung.android.bixby.agent\"\n   */\n  androidRecognitionServicePackage?: string;\n  /**\n   * Extra options to provide to the Android Recognition intent.\n   *\n   * For a full list of options, see https://developer.android.com/reference/android/speech/RecognizerIntent\n   */\n  androidIntentOptions?: Partial<AndroidIntentOptions>;\n  /**\n   * Audio source options to pass to the recognizer.\n   *\n   * This option can be used to recognize audio from a local or remote file URI.\n   */\n  audioSource?: AudioSourceOptions;\n  /**\n   * Audio recording options for persisting the audio to a local file path.\n   */\n  recordingOptions?: RecordingOptions;\n  /**\n   * Default: `\"android.speech.action.RECOGNIZE_SPEECH\"`\n   *\n   * The kind of intent action\n   *\n   * Intents:\n   *\n   * - [`android.speech.action.RECOGNIZE_SPEECH`](https://developer.android.com/reference/android/speech/RecognizerIntent#ACTION_RECOGNIZE_SPEECH) which performs speech recognition (default)\n   * - [`android.speech.action.VOICE_SEARCH_HANDS_FREE`](https://developer.android.com/reference/android/speech/RecognizerIntent#ACTION_VOICE_SEARCH_HANDS_FREE) - prompts the user for speech without requiring the user's visual attention or touch input\n   * - [`android.speech.action.WEB_SEARCH`](https://developer.android.com/reference/android/speech/RecognizerIntent#ACTION_WEB_SEARCH) - displays a web search result or trigger another type of action based on the user's speech.\n   */\n  androidIntent?:\n    | \"android.speech.action.RECOGNIZE_SPEECH\"\n    | \"android.speech.action.VOICE_SEARCH_HANDS_FREE\"\n    | \"android.speech.action.WEB_SEARCH\";\n\n  /**\n   * The hint for the speech recognition task.\n   *\n   * Default: `\"unspecified\"`\n   *\n   * Docs: https://developer.apple.com/documentation/speech/sfspeechrecognitiontaskhint\n   */\n  iosTaskHint?: IOSTaskHintValue;\n\n  /**\n   * The audio category for the speech recognition task.\n   *\n   * Use this option to configure the default audio session category and mode prior to starting speech recognition.\n   *\n   * **Caution:** confirm that the category, options and mode are compatible with the audio source.\n   *\n   * By default, the audio session category and mode are set to:\n   *\n   * - category: `playAndRecord`\n   * - options: `defaultToSpeaker` and `allowBluetooth`\n   * - mode: `measurement`\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/category\n   */\n  iosCategory?: SetCategoryOptions;\n\n  /**\n   * Settings for volume change events.\n   */\n  volumeChangeEventOptions?: {\n    /**\n     * Whether to emit volume change events.\n     *\n     * Default: false\n     */\n    enabled?: boolean;\n    /**\n     * Specifies the interval (in milliseconds) to emit `volumechange` events.\n     *\n     * Default: 100ms on iOS\n     *\n     * Increasing this value will improve performance\n     */\n    intervalMillis?: number;\n  };\n\n  /**\n   * [iOS only] Enabling this option will prevent microphone feedback.\n   *\n   * When enabled, extra signal processing is applied on the incoming audio, and any audio that is coming from the device is taken out.\n   *\n   * (This option will place both input and output nodes in voice processing mode as noted in Apple docs: http://developer.apple.com/videos/play/wwdc2019/510/?time=66)\n   */\n  iosVoiceProcessingEnabled?: boolean;\n};\n\nexport type IOSTaskHintValue = (typeof TaskHintIOS)[keyof typeof TaskHintIOS];\n\nexport type RecordingOptions = {\n  /**\n   * Whether to persist the audio to a local file path.\n   *\n   * Default: false\n   */\n  persist: boolean;\n  /**\n   * Default: `FileSystem.CacheDirectory`. This changes the default storage location for the audio file.\n   */\n  outputDirectory?: string;\n  /**\n   * Default: `\"recording_${timestamp|uuid}.[wav|caf]\"`. This changes the file name for the audio file.\n   */\n  outputFileName?: string;\n  /**\n   * Default: undefined. The sample rate of the output audio file.\n   *\n   * Only supported on iOS\n   *\n   * Default sample rate is: 16000 on Android, 44100/48000 on iOS\n   */\n  outputSampleRate?: number;\n  /**\n   * Default: undefined. The encoding of the output audio file.\n   *\n   * Only supported on iOS\n   */\n  outputEncoding?:\n    | \"pcmFormatFloat32\"\n    | \"pcmFormatFloat64\"\n    | \"pcmFormatInt16\"\n    | \"pcmFormatInt32\";\n};\n\nexport type AudioSourceOptions = {\n  /**\n   * Local audio source URI.\n   *\n   * e.g. `\"file:///storage/emulated/0/Download/audio.wav\"`\n   */\n  uri: string;\n  /**\n   * [Android only] The number of channels in the source audio.\n   *\n   * Default: 1\n   */\n  audioChannels?: number;\n  /**\n   * [Android only] A value from [AudioFormat](https://developer.android.com/reference/android/media/AudioFormat).\n   *\n   * Use the `AudioEncodingAndroid` enum to get the correct value.\n   */\n  audioEncoding?: AudioEncodingAndroidValue;\n  /**\n   * [Android only] Audio sampling rate in Hz.\n   *\n   * Default: 16000\n   */\n  sampleRate?: number;\n  /**\n   * [Android only] The delay for a 4KiB chunk of audio to stream to the speech recognition service.\n   *\n   * Use this setting to avoid being rate-limited when using network-based recognition.\n   *\n   * If you're using on-device recognition, you may want to increase this value to avoid unprocessed audio chunks.\n   *\n   * Default: 50ms for network-based recognition, 15ms for on-device recognition\n   */\n  chunkDelayMillis?: number;\n};\n\nexport type AudioEncodingAndroidValue =\n  (typeof AudioEncodingAndroid)[keyof typeof AudioEncodingAndroid];\n\nexport type AndroidIntentOptions = {\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_CALLING_PACKAGE\n   *\n   * The extra key used in an intent to the speech recognizer for voice search. Not generally to be used by developers. The system search dialog uses this, for example, to set a calling package for identification by a voice search API. If this extra is set by anyone but the system process, it should be overridden by the voice search implementation.\n   */\n  EXTRA_CALLING_PACKAGE: string;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_ENABLE_BIASING_DEVICE_CONTEXT\n   *\n   * Optional boolean to enable biasing towards device context. The recognizer will use the device context to tune the recognition results.\n   *\n   * Depending on the recognizer implementation, this value may have no effect.\n   */\n  EXTRA_ENABLE_BIASING_DEVICE_CONTEXT: boolean;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_ENABLE_LANGUAGE_DETECTION\n   *\n   * Optional boolean indicating whether to enable language detection. When enabled, the recognizer will consistently identify the language of the current spoken utterance and provide that info via RecognitionListener#onLanguageDetection(Bundle).\n   */\n  EXTRA_ENABLE_LANGUAGE_DETECTION: boolean;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_ENABLE_LANGUAGE_SWITCH\n   *\n   * Optional string to enable automatic switching to the language being spoken with the desired sensitivity level, instead of being restricted to a single language. The corresponding language models must be downloaded to support the switch. Otherwise, the recognizer will report an error on a switch failure. The recognizer provides the switch results via RecognitionListener#onLanguageDetection(Bundle).\n   *\n   * Since detection is a necessary requirement for the language switching, setting this value implicitly enables EXTRA_ENABLE_LANGUAGE_DETECTION.\n   *\n   * Depending on the recognizer implementation, this value may have no effect.\n   */\n  EXTRA_ENABLE_LANGUAGE_SWITCH: (typeof RecognizerIntentEnableLanguageSwitch)[keyof typeof RecognizerIntentEnableLanguageSwitch];\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_ENABLE_FORMATTING\n   *\n   * NOTE: This is also configurable through `addsPunctuation` (which sets `EXTRA_ENABLE_FORMATTING` to \"quality\")\n   *\n   * [API level 33] Optional string to enable text formatting (e.g. unspoken punctuation (examples: question mark, comma, period, etc.), capitalization, etc.) and specify the optimization strategy. If set, the partial and final result texts will be formatted. Each result list will contain two hypotheses in the order of 1) formatted text 2) raw text.\n   *\n   */\n  EXTRA_ENABLE_FORMATTING: \"latency\" | \"quality\";\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_HIDE_PARTIAL_TRAILING_PUNCTUATION\n   *\n   * [API level 33] Optional boolean, to be used with EXTRA_ENABLE_FORMATTING, to prevent the recognizer adding punctuation after the last word of the partial results. The default is false.\n   */\n  EXTRA_HIDE_PARTIAL_TRAILING_PUNCTUATION: boolean;\n  /**\n   * Optional list of IETF language tags (as defined by BCP 47, e.g. \"en-US\", \"de-DE\").\n   *\n   * [API level 34] This extra is to be used with EXTRA_ENABLE_LANGUAGE_DETECTION. If set, the recognizer will constrain the language detection output to this list of languages, potentially improving detection accuracy.\n   */\n  EXTRA_LANGUAGE_DETECTION_ALLOWED_LANGUAGES: string[];\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_LANGUAGE_MODEL\n   *\n   * [Default: free_form] The language model to use for speech recognition.\n   *\n   * Informs the recognizer which speech model to prefer when performing ACTION_RECOGNIZE_SPEECH.\n   * The recognizer uses this information to fine tune the results.\n   * This extra is required. Activities implementing ACTION_RECOGNIZE_SPEECH may interpret the values as they see fit.\n   */\n  EXTRA_LANGUAGE_MODEL: (typeof RecognizerIntentExtraLanguageModel)[keyof typeof RecognizerIntentExtraLanguageModel];\n\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_LANGUAGE_SWITCH_ALLOWED_LANGUAGES\n   *\n   * [API level 34] Optional list of IETF language tags (as defined by BCP 47, e.g. \"en-US\", \"de-DE\").\n   * This extra is to be used with EXTRA_ENABLE_LANGUAGE_SWITCH. If set, the recognizer will apply the auto switch only to these languages, even if the speech models of other languages also exist.\n   * The corresponding language models must be downloaded to support the switch. Otherwise, the recognizer will report an error on a switch failure.\n   */\n  EXTRA_LANGUAGE_SWITCH_ALLOWED_LANGUAGES: string[];\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_LANGUAGE_SWITCH_INITIAL_ACTIVE_DURATION_TIME_MILLIS\n   *\n   * [API level 35] Optional integer to use for EXTRA_ENABLE_LANGUAGE_SWITCH. If set, the language switch will only be activated for this value of ms of audio since the START_OF_SPEECH. This could provide a more stable recognition result when the language switch is only required in the beginning of the session.\n   */\n  EXTRA_LANGUAGE_SWITCH_INITIAL_ACTIVE_DURATION_TIME_MILLIS: number;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_LANGUAGE_SWITCH_MAX_SWITCHES\n   *\n   * [API level 35] Optional integer to use for EXTRA_ENABLE_LANGUAGE_SWITCH. If set, the language switch will be deactivated when LANGUAGE_SWITCH_MAX_SWITCHES reached.\n   *\n   * Depending on the recognizer implementation, this flag may have no effect.\n   */\n  EXTRA_LANGUAGE_SWITCH_MAX_SWITCHES: number;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_MASK_OFFENSIVE_WORDS\n   *\n   * [API level 33] Optional boolean indicating whether the recognizer should mask the offensive words in recognition results. The Default is true.\n   *\n   * Constant Value: \"android.speech.extra.MASK_OFFENSIVE_WORDS\"\n   */\n  EXTRA_MASK_OFFENSIVE_WORDS: boolean;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_ORIGIN\n   *\n   * Optional value which can be used to indicate the referer url of a page in which speech was requested. For example, a web browser may choose to provide this for uses of speech on a given page.\n   */\n  EXTRA_ORIGIN: string;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_PREFER_OFFLINE\n   *\n   * Optional boolean, to be used with ACTION_RECOGNIZE_SPEECH, ACTION_VOICE_SEARCH_HANDS_FREE, ACTION_WEB_SEARCH to indicate whether to only use an offline speech recognition engine. The default is false, meaning that either network or offline recognition engines may be used.\n   *\n   * Depending on the recognizer implementation, these values may have no effect.\n   */\n  EXTRA_PREFER_OFFLINE: boolean;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_PROMPT\n   *\n   * Optional text prompt to show to the user when asking them to speak.\n   */\n  EXTRA_PROMPT: string;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_REQUEST_WORD_CONFIDENCE\n   *\n   * [API level 34] Optional boolean indicating whether the recognizer should return the confidence level of each word in the final recognition results.\n   */\n  EXTRA_REQUEST_WORD_CONFIDENCE: boolean;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_REQUEST_WORD_TIMING\n   *\n   * [API level 34] Optional boolean indicating whether the recognizer should return the timestamp of each word in the final recognition results.\n   */\n  EXTRA_REQUEST_WORD_TIMING: boolean;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_SECURE\n   *\n   * Optional boolean to indicate that a \"hands free\" voice search was performed while the device was in a secure mode. An example of secure mode is when the device's screen lock is active, and it requires some form of authentication to be unlocked. When the device is securely locked, the voice search activity should either restrict the set of voice actions that are permitted, or require some form of secure authentication before proceeding.\n   */\n  EXTRA_SECURE: boolean;\n  /**\n   * Optional string to enable segmented session mode of the specified type,\n   * which can be `EXTRA_AUDIO_SOURCE`, `EXTRA_SPEECH_INPUT_MINIMUM_LENGTH_MILLIS` or `EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS`.\n   * When segmented session mode is supported by the recognizer implementation and this extra is set,\n   * it will return the recognition results in segments via [RecognitionListener#onSegmentResults(Bundle)](https://developer.android.com/reference/android/speech/RecognitionListener#onSegmentResults(android.os.Bundle))\n   * and terminate the session with [RecognitionListener#onEndOfSegmentedSession()](https://developer.android.com/reference/android/speech/RecognitionListener#onEndOfSegmentedSession()).\n   *\n   * When setting this extra, make sure the extra used as the string value here is also set in the same intent with proper value.\n   *\n   * Depending on the recognizer implementation, this value may have no effect.\n   */\n  EXTRA_SEGMENTED_SESSION:\n    | \"android.speech.extra.AUDIO_SOURCE\"\n    | \"android.speech.extras.SPEECH_INPUT_MINIMUM_LENGTH_MILLIS\"\n    | \"android.speech.extras.SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS\";\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS\n   *\n   * The amount of time that it should take after the recognizer stops hearing speech to consider the input complete hence end the recognition session.\n   *\n   * Note that it is extremely rare you'd want to specify this value in an intent. Generally, it should be specified only when it is also used as the value for EXTRA_SEGMENTED_SESSION to enable segmented session mode. Note also that certain values may cause undesired or unexpected results - use judiciously!\n   *\n   * Depending on the recognizer implementation, these values may have no effect.\n   *\n   */\n  EXTRA_SPEECH_INPUT_COMPLETE_SILENCE_LENGTH_MILLIS: number;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_SPEECH_INPUT_MINIMUM_LENGTH_MILLIS\n   *\n   * Optional integer to indicate the minimum length of the recognition session. The recognizer will not stop recognizing speech before this amount of time.\n   *\n   * Note that it is extremely rare you'd want to specify this value in an intent. Generally, it should be specified only when it is also used as the value for EXTRA_SEGMENTED_SESSION to enable segmented session mode. Note also that certain values may cause undesired or unexpected results - use judiciously!\n   *\n   * Depending on the recognizer implementation, these values may have no effect.\n   */\n  EXTRA_SPEECH_INPUT_MINIMUM_LENGTH_MILLIS: number;\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS\n   *\n   * The amount of time that it should take after we stop hearing speech to consider the input possibly complete.\n   * This is used to prevent the endpointer cutting off during very short mid-speech pauses.\n   * Note that it is extremely rare you'd want to specify this value in an intent.\n   * If you don't have a very good reason to change these, you should leave them as they are.\n   * Note also that certain values may cause undesired or unexpected results - use judiciously!\n   * Additionally, depending on the recognizer implementation, these values may have no effect.\n   */\n  EXTRA_SPEECH_INPUT_POSSIBLY_COMPLETE_SILENCE_LENGTH_MILLIS: number;\n};\n\nexport type ExpoSpeechRecognitionNativeEvents = {\n  [K in keyof ExpoSpeechRecognitionNativeEventMap]: (\n    event: ExpoSpeechRecognitionNativeEventMap[K],\n  ) => void;\n};\n\nexport declare class ExpoSpeechRecognitionModuleType extends NativeModule<ExpoSpeechRecognitionNativeEvents> {\n  /**\n   * Starts speech recognition.\n   */\n  start(options: ExpoSpeechRecognitionOptions): void;\n  /**\n   * Stops speech recognition and attempts to return a final result (through the `result` event).\n   */\n  stop(): void;\n  /**\n   * Cancels speech recognition immediately without returning a final result.\n   */\n  abort(): void;\n  /**\n   * Presents a dialog to the user to request permissions for using speech recognition and the microphone.\n   *\n   * For Android, this will request RECORD_AUDIO permission.\n   *\n   * For iOS, this will request microphone and speech recognition permissions.\n   * Once a user has granted (or denied) permissions by responding to the original permission request dialog,\n   * the only way that the permissions can be changed is by the user themselves using the device settings app.\n   */\n  requestPermissionsAsync(): Promise<ExpoSpeechRecognitionPermissionResponse>;\n  /**\n   * Returns the current permission status for speech recognition and the microphone.\n   *\n   * You may also use `getMicrophonePermissionsAsync` and `getSpeechRecognizerPermissionsAsync` to get the permissions separately.\n   */\n  getPermissionsAsync(): Promise<ExpoSpeechRecognitionPermissionResponse>;\n  /**\n   * Returns the current permission status for the microphone.\n   */\n  getMicrophonePermissionsAsync(): Promise<PermissionResponse>;\n  /**\n   * Presents a dialog to the user to request permissions for using the microphone.\n   *\n   * For iOS, once a user has granted (or denied) permissions by responding to the original permission request dialog,\n   * the only way that the permissions can be changed is by the user themselves using the device settings app.\n   */\n  requestMicrophonePermissionsAsync(): Promise<PermissionResponse>;\n  /**\n   * Returns the current permission status for speech recognition.\n   */\n  getSpeechRecognizerPermissionsAsync(): Promise<ExpoSpeechRecognitionPermissionResponse>;\n  /**\n   * [iOS only] Presents a dialog to the user to request permissions for using the speech recognizer.\n   * This permission is required when `requiresOnDeviceRecognition` is disabled (i.e. network-based recognition)\n   *\n   * For iOS, once a user has granted (or denied) permissions by responding to the original permission request dialog,\n   * the only way that the permissions can be changed is by the user themselves using the device settings app.\n   */\n  requestSpeechRecognizerPermissionsAsync(): Promise<ExpoSpeechRecognitionPermissionResponse>;\n  /**\n   * Returns an array of locales supported by the speech recognizer.\n   *\n   * Not supported on Android 12 and below (API level 31), this will return an empty array of locales.\n   *\n   * @throws {\"package_not_found\"} If the service package is not found.\n   * @throws {\"error_[number]\"} If there was an error retrieving the supported locales.\n   */\n  getSupportedLocales(options: {\n    /**\n     * The package name of the speech recognition service to use.\n     *\n     * e.g. \"com.google.android.as\" or \"com.samsung.android.bixby.agent\"\n     *\n     * Warning: the service package (such as Bixby) may not be able to return any results.\n     */\n    androidRecognitionServicePackage?: string;\n  }): Promise<{\n    /**\n     * All supported languages on the device. This includes both installed and supported languages.\n     */\n    locales: string[];\n    /**\n     * These languages are installed on to the device for offline use.\n     *\n     * This will likely be an empty array if the service package is not \"com.google.android.as\"\n     */\n    installedLocales: string[];\n  }>;\n  /**\n   * [Android only] Returns an array of package names of speech recognition services that are available on the device.\n   *\n   * List of all available services as bundle identifiers, e.g. [\"com.google.android.as\", \"com.google.android.tts\"]\n   */\n  getSpeechRecognitionServices(): string[];\n  /**\n   * [Android only] Returns the default voice recognition service on the device.\n   *\n   * @returns empty string if no service is found, or not Android\n   */\n  getDefaultRecognitionService(): {\n    /** e.g. \"com.google.android.tts\" or \"com.google.android.googlequicksearchbox\" */\n    packageName: string;\n  };\n  /**\n   * [Android only] Returns the default voice recognition service on the device.\n   *\n   * e.g. \"com.google.android.googlequicksearchbox\" or \"com.samsung.android.bixby.agent\"\n   *\n   * @returns empty string if no service is found, or not Android\n   */\n  getAssistantService(): {\n    /** e.g. \"com.google.android.googlequicksearchbox\" or \"com.samsung.android.bixby.agent\" */\n    packageName: string;\n  };\n  /**\n   * Whether the on-device speech recognition is available on the device.\n   */\n  supportsOnDeviceRecognition(): boolean;\n  /**\n   * Whether the recording feature is available on the device.\n   *\n   * This mostly applies to Android devices, to check if it's greater than Android 13.\n   */\n  supportsRecording(): boolean;\n  /**\n   * Whether on-device speech recognition is available.\n   *\n   * If this method returns false, `start()` will fail and emit an error event with the code `service-not-allowed` or `language-not-supported`.\n   */\n  isRecognitionAvailable(): boolean;\n\n  /**\n   * Downloads the offline model for the specified locale.\n   * Note: this is only supported on Android 13 and above.\n   */\n  androidTriggerOfflineModelDownload(options: {\n    /** The locale to download the model for, e.g. \"en-US\" */\n    locale: string;\n  }): Promise<{\n    /**\n     * On Android 13, the status will be \"opened_dialog\" indicating that the model download dialog was opened.\n     * On Android 14+, the status will be \"download_success\" indicating that the model download was successful.\n     * On Android 14+, \"download_canceled\" will be returned if the download was canceled by a user interaction.\n     */\n    status: \"download_success\" | \"opened_dialog\" | \"download_canceled\";\n    message: string;\n  }>;\n  /**\n   * [iOS only] For advanced use cases, you may use this function to set the audio session category and mode.\n   *\n   * See: https://developer.apple.com/documentation/avfaudio/avaudiosession/1771734-setcategory\n   */\n  setCategoryIOS(options: SetCategoryOptions): void;\n\n  /**\n   * [iOS only] Returns the current audio session category and options.\n   *\n   * See:\n   * - [AVAudioSession.Category](https://developer.apple.com/documentation/avfaudio/avaudiosession/category)\n   * - [AVAudioSession.CategoryOptions](https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions)\n   * - [AVAudioSession.Mode](https://developer.apple.com/documentation/avfaudio/avaudiosession/mode)\n   */\n  getAudioSessionCategoryAndOptionsIOS(): {\n    category: AVAudioSessionCategoryValue;\n    categoryOptions: AVAudioSessionCategoryOptionsValue[];\n    mode: AVAudioSessionModeValue;\n  };\n  /**\n   * [iOS only] Sets the shared audio session active state.\n   *\n   * Calls the following on iOS: `AVAudioSession.sharedInstance().setActive(value, options)`\n   *\n   * See: https://developer.apple.com/documentation/avfaudio/avaudiosession/1616627-setactive\n   */\n  setAudioSessionActiveIOS(\n    value: boolean,\n    options?: {\n      /** [Default: true] Whether to notify other audio sessions when the active state changes. */\n      notifyOthersOnDeactivation: boolean;\n    },\n  ): void;\n  /**\n   * Returns the current state of the speech recognizer.\n   */\n  getStateAsync(): Promise<SpeechRecognitionState>;\n}\n\nexport type SetCategoryOptions = {\n  category: AVAudioSessionCategoryValue;\n  categoryOptions: AVAudioSessionCategoryOptionsValue[];\n  mode?: AVAudioSessionModeValue;\n};\n\ntype SpeechRecognitionState =\n  | \"inactive\"\n  | \"starting\"\n  | \"recognizing\"\n  | \"stopping\";\n\n/**\n * [iOS only] See: [AVAudioSession.CategoryOptions](https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions)\n *\n * Use the `AVAudioSessionCategoryOptions` enum to get the correct value.\n */\nexport type AVAudioSessionCategoryOptionsValue =\n  (typeof AVAudioSessionCategoryOptions)[keyof typeof AVAudioSessionCategoryOptions];\n\n/**\n * [iOS only] See: [AVAudioSession.Mode](https://developer.apple.com/documentation/avfaudio/avaudiosession/mode)\n *\n * Use the `AVAudioSessionMode` enum to get the correct value.\n */\nexport type AVAudioSessionModeValue =\n  (typeof AVAudioSessionMode)[keyof typeof AVAudioSessionMode];\n"]}