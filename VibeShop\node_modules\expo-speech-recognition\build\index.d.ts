export { ExpoWebSpeechRecognition, ExpoWebSpeechGrammar, ExpoWebSpeechGrammarList, } from "./ExpoWebSpeechRecognition";
export { ExpoSpeechRecognitionModule } from "./ExpoSpeechRecognitionModule";
export { useSpeechRecognitionEvent } from "./useSpeechRecognitionEvent";
export { AVAudioSessionCategory, AVAudioSessionCategoryOptions, AVAudioSessionMode, RecognizerIntentExtraLanguageModel, RecognizerIntentEnableLanguageSwitch, AudioEncodingAndroid, TaskHintIOS, } from "./constants";
export declare const getSupportedLocales: (options?: {
    androidRecognitionServicePackage?: string;
}) => Promise<{
    locales: string[];
    installedLocales: string[];
}>;
export declare const getSpeechRecognitionServices: () => string[];
export declare const supportsOnDeviceRecognition: () => boolean;
export declare const supportsRecording: () => boolean;
export declare const setCategoryIOS: (options: import("./ExpoSpeechRecognitionModule.types").SetCategoryOptions) => void;
export declare const getAudioSessionCategoryAndOptionsIOS: () => {
    category: import("./ExpoSpeechRecognitionModule.types").AVAudioSessionCategoryValue;
    categoryOptions: import("./ExpoSpeechRecognitionModule.types").AVAudioSessionCategoryOptionsValue[];
    mode: import("./ExpoSpeechRecognitionModule.types").AVAudioSessionModeValue;
};
export declare const setAudioSessionActiveIOS: (value: boolean, options?: {
    notifyOthersOnDeactivation: boolean;
}) => void;
export declare const androidTriggerOfflineModelDownload: (options: {
    locale: string;
}) => Promise<{
    status: "download_success" | "opened_dialog" | "download_canceled";
    message: string;
}>;
export declare const isRecognitionAvailable: () => boolean;
export declare const getDefaultRecognitionService: () => {
    packageName: string;
};
export declare const getAssistantService: () => {
    packageName: string;
};
export declare const addSpeechRecognitionListener: <EventName extends keyof import("./ExpoSpeechRecognitionModule.types").ExpoSpeechRecognitionNativeEventMap>(eventName: EventName, listener: import("./ExpoSpeechRecognitionModule.types").ExpoSpeechRecognitionNativeEvents[EventName]) => import("expo-modules-core/build/ts-declarations/EventEmitter").EventSubscription;
export type { ExpoSpeechRecognitionOptions, AndroidIntentOptions, ExpoSpeechRecognitionNativeEventMap, AVAudioSessionCategoryOptionsValue, AVAudioSessionModeValue, AVAudioSessionCategoryValue, AudioEncodingAndroidValue, AudioSourceOptions, RecordingOptions, IOSTaskHintValue, SetCategoryOptions, ExpoSpeechRecognitionErrorCode, ExpoSpeechRecognitionErrorEvent, ExpoSpeechRecognitionResultEvent, ExpoSpeechRecognitionResult, ExpoSpeechRecognitionResultSegment, } from "./ExpoSpeechRecognitionModule.types";
//# sourceMappingURL=index.d.ts.map