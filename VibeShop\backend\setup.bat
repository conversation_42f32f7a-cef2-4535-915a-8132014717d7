@echo off
echo Setting up VibeShop Voice Recognition Backend...

REM Create virtual environment
echo Creating virtual environment...
python -m venv venv

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

echo Setup complete!
echo.
echo To run the server:
echo 1. Activate the virtual environment: venv\Scripts\activate
echo 2. Run the server: python run.py
echo.
echo Press any key to exit...
pause > nul 