{"version": 3, "file": "ExpoWebSpeechRecognition.js", "sourceRoot": "", "sources": ["../src/ExpoWebSpeechRecognition.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAM5E,MAAM,IAAI,GAAG,GAAG,EAAE,GAAE,CAAC,CAAC;AAEtB,MAAM,eAAe,GAAG,CAAC,MAAmB,EAAE,EAAE,CAAC,CAAC;IAChD,SAAS,EAAE,CAAU;IACrB,OAAO,EAAE,KAAK;IACd,cAAc,EAAE,CAAU;IAC1B,UAAU,EAAE,KAAK;IACjB,eAAe,EAAE,CAAU;IAC3B,QAAQ,EAAE,KAAK;IACf,YAAY,EAAE,GAAG,EAAE,CAAC,EAAE;IACtB,aAAa,EAAE,MAAM;IACrB,gBAAgB,EAAE,KAAK;IACvB,UAAU,EAAE,CAAC;IACb,SAAS,EAAE,IAAI;IACf,IAAI,EAAE,CAAU;IAChB,cAAc,EAAE,IAAI;IACpB,WAAW,EAAE,CAAC;IACd,wBAAwB,EAAE,IAAI;IAC9B,eAAe,EAAE,IAAI;IACrB,MAAM;IACN,SAAS,EAAE,CAAC;IACZ,IAAI,EAAE,EAAE;IACR,YAAY,EAAE,KAAK;IACnB,WAAW,EAAE,KAAK;IAClB,UAAU,EAAE,IAAI;IAChB,SAAS,EAAE,IAAI;CAChB,CAAC,CAAC;AAYH,SAAS,SAAS,CAChB,SAAY,EACZ,QAAkC,EAClC,QAAyD;IAEzD,OAAO;QACL,SAAS;QACT,cAAc,EAAE,CAAC,WAAW,EAAE,EAAE,CAC9B,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC;KACrD,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,uBAAuB,GAQzB;IACF,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QACjC,OAAO;YACL,SAAS,EAAE,YAAY;YACvB,cAAc,CAAC,WAAW;gBACxB,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACtB,GAAG,eAAe,CAAC,QAAQ,CAAC;oBAC5B,GAAG,EAAE,WAAW,CAAC,GAAG;iBACrB,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;IACD,QAAQ,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAC/B,OAAO;YACL,SAAS,EAAE,UAAU;YACrB,cAAc,CAAC,WAAW;gBACxB,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE;oBACtB,GAAG,eAAe,CAAC,QAAQ,CAAC;oBAC5B,GAAG,EAAE,WAAW,CAAC,GAAG;iBACrB,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;IACD,OAAO,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAC9B,aAAa;QACb,OAAO,SAAS,CAAC,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;IACD,GAAG,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAC1B,OAAO,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC9C,CAAC;IACD,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAC5B,OAAO;YACL,SAAS,EAAE,OAAO;YAClB,cAAc;gBACZ,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;YACrD,CAAC;SACF,CAAC;IACJ,CAAC;IACD,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAC5B,OAAO;YACL,SAAS,EAAE,OAAO;YAClB,cAAc,EAAE,CACd,WAAyD,EACzD,EAAE;gBACF,MAAM,WAAW,GAAuC;oBACtD,GAAG,eAAe,CAAC,QAAQ,CAAC;oBAC5B,sCAAsC;oBACtC,KAAK,EAAE,WAAW,CAAC,KAAmC;oBACtD,OAAO,EAAE,WAAW,CAAC,OAAO;iBAC7B,CAAC;gBACF,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YACvC,CAAC;SACF,CAAC;IACJ,CAAC;IACD,MAAM,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;QAC7B,OAAO;YACL,SAAS,EAAE,QAAQ;YACnB,cAAc,EAAE,CACd,WAA0D,EAC1D,EAAE;gBACF,IAAI,CAAC,QAAQ,CAAC,cAAc,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;oBACrD,OAAO;gBACT,CAAC;gBACD,MAAM,YAAY,GAAG,WAAW,CAAC,OAAO,CAAC,GAAG,CAC1C,CAAC,MAAM,EAAE,EAAE,CACT,IAAI,gCAAgC,CAClC,MAAM,CAAC,UAAU,EACjB,MAAM,CAAC,UAAU,CAClB,CACJ,CAAC;gBACF,MAAM,WAAW,GAAwC;oBACvD,GAAG,eAAe,CAAC,QAAQ,CAAC;oBAC5B,OAAO,EAAE,IAAI,+BAA+B,CAAC;wBAC3C,IAAI,2BAA2B,CAAC,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC;qBACnE,CAAC;iBACH,CAAC;gBACF,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YACvC,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC;AAOF,8FAA8F;AAC9F,MAAM,OAAO,wBAAwB;IACnC,IAAI,GAAG,OAAO,CAAC;IACf,QAAQ,GAAsB,IAAI,wBAAwB,EAAE,CAAC;IAC7D,eAAe,GAAG,CAAC,CAAC;IACpB,UAAU,GAAG,KAAK,CAAC;IAEnB,eAAe,GAAG,KAAK,CAAC;IAExB,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,IAAI,cAAc,CAAC,cAAuB;QACxC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;QACtC,sBAAsB;IACxB,CAAC;IAED,sBAAsB;IAEtB,gIAAgI;IAChI,iBAAiB,GAAc,SAAS,CAAC;IACzC,uIAAuI;IACvI,2BAA2B,GAAG,KAAK,CAAC;IACpC,oHAAoH;IACpH,eAAe,GAAG,KAAK,CAAC;IACxB,yFAAyF;IACzF,oBAAoB,CAAuD;IAC3E,qFAAqF;IACrF,WAAW,CAA+C;IAC1D,wFAAwF;IACxF,gBAAgB,CAAoD;IACpE,8FAA8F;IAC9F,aAAa,GAAmD,SAAS,CAAC;IAC1E,+EAA+E;IAC/E,WAAW,GAAiD,SAAS,CAAC;IACtE,oFAAoF;IACpF,WAAW,GAAiD,SAAS,CAAC;IACtE;;;;;;;;;OASG;IACH,gCAAgC,CAAmE;IAEnG,6BAA6B;IAC7B,gBAAgB,GAAkD,IAAI,GAAG,EAAE,CAAC;IAE5E,KAAK;QACH,2BAA2B,CAAC,uBAAuB,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE;YAC9D,2BAA2B;YAC3B,+DAA+D;YAC/D,2BAA2B,CAAC,KAAK,CAAC;gBAChC,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,eAAe,EAAE,IAAI,CAAC,eAAe;gBACrC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB;gBACzC,2BAA2B,EAAE,IAAI,CAAC,2BAA2B;gBAC7D,eAAe,EAAE,IAAI,CAAC,eAAe;gBACrC,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,gBAAgB,EAAE,IAAI,CAAC,gBAAgB;gBACvC,oBAAoB,EAAE,IAAI,CAAC,oBAAoB;gBAC/C,gCAAgC,EAAE,IAAI,CAAC,gCAAgC;gBACvE,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IACD,IAAI,GAAG,2BAA2B,CAAC,IAAI,CAAC;IACxC,KAAK,GAAG,2BAA2B,CAAC,KAAK,CAAC;IAE1C,QAAQ,GAAmC,IAAI,CAAC;IAChD,IAAI,OAAO,CAAC,QAAwC;QAClD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IACD,gDAAgD;IAChD,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,MAAM,GAAiC,IAAI,CAAC;IAC5C,IAAI,KAAK,CAAC,QAAsC;QAC9C,IAAI,CAAC,aAAa,CAChB,KAAK,EACL,CAAC,EAAE,EAAE,EAAE;YACL,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAC3B,CAAC,EACD,IAAI,CAAC,MAAM,CACZ,CAAC;QACF,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC;IACzB,CAAC;IACD,kEAAkE;IAClE,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,QAAQ,GAAmC,IAAI,CAAC;IAChD,IAAI,OAAO,CAAC,QAAwC;QAClD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IACD,qEAAqE;IACrE,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,aAAa,CACX,GAAM,EACN,UAAoC,EACpC,gBAA0C;QAE1C,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;QAClD,CAAC;QACD,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,SAAS,GAAoC,IAAI,CAAC;IAClD,IAAI,QAAQ,CAAC,QAAyC;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IACD;0GACsG;IACtG,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,UAAU,GAAqC,IAAI,CAAC;IACpD,IAAI,SAAS,CAAC,QAA0C;QACtD,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;QACzD,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;IAC7B,CAAC;IACD,wGAAwG;IACxG,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAED,cAAc,GAAyC,IAAI,CAAC;IAC5D,IAAI,aAAa,CAAC,QAA8C;QAC9D,IAAI,CAAC,aAAa,CAAC,aAAa,EAAE,QAAQ,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACjE,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;IACjC,CAAC;IACD,wGAAwG;IACxG,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED,YAAY,GAAuC,IAAI,CAAC;IACxD,IAAI,WAAW,CAAC,QAA4C;QAC1D,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC7D,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;IAC/B,CAAC;IACD,wGAAwG;IACxG,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,aAAa,GAAwC,IAAI,CAAC;IAC1D,IAAI,YAAY,CAAC,QAA6C;QAC5D,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC;IAChC,CAAC;IACD,8DAA8D;IAC9D,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED,WAAW,GAAsC,IAAI,CAAC;IACtD,IAAI,UAAU,CAAC,QAA2C;QACxD,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAC3D,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAC9B,CAAC;IACD,8DAA8D;IAC9D,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,aAAa;IACb,UAAU,GAAyD,IAAI,CAAC;IACxE,aAAa;IACb,YAAY,GAAyD,IAAI,CAAC;IAE1E,gBAAgB,CACd,IAAO,EACP,QAA2B,EAC3B,OAA2C;QAE3C,MAAM,IAAI,GAAG,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,CAAC,IAAI,CAAC;QAEzD,8CAA8C;QAC9C,4DAA4D;QAC5D,MAAM,eAAe,GAAG,IAAI;YAC1B,CAAC,CAAE,CAAC,CAAC,EAAE,EAAE,EAAE;gBACP,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;gBACxB,oCAAoC;gBACpC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC;oBAC5D,GAAG,CAAC,MAAM,EAAE,CAAC;gBACf,CAAC;gBACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACzC,CAAC,CAAuB;YAC1B,CAAC,CAAC,QAAQ,CAAC;QAEb,2DAA2D;QAC3D,MAAM,aAAa,GACjB,uBAAuB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,eAAe,CAAC;YACtD,SAAS,CACP,IAAI,EACJ,IAAI,EACJ,eAAkE,CACnE,CAAC;QAEJ,MAAM,YAAY,GAAG,2BAA2B,CAAC,WAAW,CAC1D,aAAa,CAAC,SAAS;QACvB,mBAAmB;QACnB,aAAa,CAAC,cAAc,CAC7B,CAAC;QAEF,sDAAsD;QACtD,iGAAiG;QACjG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC;IACtD,CAAC;IAED,mBAAmB,CACjB,IAAO,EACP,QAGQ,EACR,OAAoD;QAEpD,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC1D,IAAI,aAAa,EAAE,CAAC;YAClB,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACzC,YAAY,CAAC,MAAM,EAAE,CAAC;YACxB,CAAC;YACD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED,aAAa,CAAC,KAAY;QACxB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;CACF;AAED;;GAEG;AACH,MAAM,OAAO,wBAAwB;IACnC,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B,CAAC;IACD,SAAS,GAA2B,EAAE,CAAC;IAGvC,UAAU,CAAC,GAAW,EAAE,MAA2B;QACjD,OAAO;IACT,CAAC;IAED,IAAI,CAAC,KAAa;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC/B,CAAC;IAED,aAAa,GAAG,CAAC,OAAe,EAAE,MAAe,EAAE,EAAE;QACnD,mEAAmE;QACnE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,CAAC;QAC/D,0EAA0E;QAC1E,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC1D,CAAC,CAAC;CACH;AAED,MAAM,OAAO,oBAAoB;IAC/B,GAAG,GAAG,EAAE,CAAC;IACT,MAAM,GAAG,CAAC,CAAC;IAEX,YAAY,GAAW,EAAE,MAAe;QACtC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,CAAC;IAC5B,CAAC;CACF;AAED,MAAM,+BAA+B;IACnC,QAAQ,GAAkC,EAAE,CAAC;IAE7C,CAAC,MAAM,CAAC,QAAQ,CAAC;QACf,OAAO,IAAI,CAAC,QAAQ,CAClB,MAAM,CAAC,QAAQ,CAChB,EAAgD,CAAC;IACpD,CAAC;IACD,MAAM,CAAS;IACf,IAAI,CAAC,KAAa;QAChB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC;IAGD,YAAY,OAAsC;QAChD,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;IACH,CAAC;CACF;AAED,MAAM,2BAA2B;IAC/B,aAAa,GAAuC,EAAE,CAAC;IACvD,kGAAkG;IACzF,OAAO,CAAU;IAC1B,iGAAiG;IACjG,MAAM,CAAS;IACf,+FAA+F;IAC/F,IAAI,CAAC,KAAa;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IACnC,CAAC;IAED,CAAC,MAAM,CAAC,QAAQ,CAAC;QACf,OAAO,IAAI,CAAC,aAAa,CACvB,MAAM,CAAC,QAAQ,CAChB,EAAiD,CAAC;IACrD,CAAC;IAED,YACE,OAAgB,EAChB,YAAgD;QAEhD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;QAClC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;CACF;AAED,MAAM,gCAAgC;IACpC,UAAU,CAAS;IACnB,UAAU,CAAS;IAEnB,YAAY,UAAkB,EAAE,UAAkB;QAChD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;IAC/B,CAAC;CACF", "sourcesContent": ["// Import the native module. On web, it will be resolved to ExpoSpeechRecognition.web.ts\n// and on native platforms to ExpoSpeechRecognition.ts\nimport type { EventSubscription } from \"expo-modules-core\";\nimport { ExpoSpeechRecognitionModule } from \"./ExpoSpeechRecognitionModule\";\nimport type {\n  ExpoSpeechRecognitionNativeEventMap,\n  ExpoSpeechRecognitionOptions,\n} from \"./ExpoSpeechRecognitionModule.types\";\n\nconst noop = () => {};\n\nconst createEventData = (target: EventTarget) => ({\n  AT_TARGET: 2 as const,\n  bubbles: false,\n  BUBBLING_PHASE: 3 as const,\n  cancelable: false,\n  CAPTURING_PHASE: 1 as const,\n  composed: false,\n  composedPath: () => [],\n  currentTarget: target,\n  defaultPrevented: false,\n  eventPhase: 0,\n  isTrusted: true,\n  NONE: 0 as const,\n  preventDefault: noop,\n  resultIndex: 0,\n  stopImmediatePropagation: noop,\n  stopPropagation: noop,\n  target,\n  timeStamp: 0,\n  type: \"\",\n  cancelBubble: false,\n  returnValue: false,\n  srcElement: null,\n  initEvent: noop,\n});\n\ntype NativeEventAndListener<\n  TEventName extends keyof ExpoSpeechRecognitionNativeEventMap,\n> = {\n  /** Event name to listen for on native side */\n  eventName: TEventName;\n  nativeListener: (\n    nativeEvent: ExpoSpeechRecognitionNativeEventMap[TEventName],\n  ) => void;\n};\n\nfunction stubEvent<K extends keyof SpeechRecognitionEventMap>(\n  eventName: K,\n  instance: ExpoWebSpeechRecognition,\n  listener: (this: SpeechRecognition, ev: Event) => unknown,\n): NativeEventAndListener<K> {\n  return {\n    eventName,\n    nativeListener: (nativeEvent) =>\n      listener.call(instance, createEventData(instance)),\n  };\n}\n\n/**\n * Transforms the native listener payloads to web-compatible shapes\n */\nconst WebListenerTransformers: {\n  [K in keyof SpeechRecognitionEventMap]?: (\n    instance: ExpoWebSpeechRecognition,\n    listener: (\n      this: SpeechRecognition,\n      ev: SpeechRecognitionEventMap[K],\n    ) => unknown,\n  ) => NativeEventAndListener<K>;\n} = {\n  audiostart: (instance, listener) => {\n    return {\n      eventName: \"audiostart\",\n      nativeListener(nativeEvent) {\n        listener.call(instance, {\n          ...createEventData(instance),\n          uri: nativeEvent.uri,\n        });\n      },\n    };\n  },\n  audioend: (instance, listener) => {\n    return {\n      eventName: \"audioend\",\n      nativeListener(nativeEvent) {\n        listener.call(instance, {\n          ...createEventData(instance),\n          uri: nativeEvent.uri,\n        });\n      },\n    };\n  },\n  nomatch: (instance, listener) => {\n    // @ts-ignore\n    return stubEvent(\"nomatch\", instance, listener);\n  },\n  end: (instance, listener) => {\n    return stubEvent(\"end\", instance, listener);\n  },\n  start: (instance, listener) => {\n    return {\n      eventName: \"start\",\n      nativeListener() {\n        listener.call(instance, createEventData(instance));\n      },\n    };\n  },\n  error: (instance, listener) => {\n    return {\n      eventName: \"error\",\n      nativeListener: (\n        nativeEvent: ExpoSpeechRecognitionNativeEventMap[\"error\"],\n      ) => {\n        const clientEvent: SpeechRecognitionEventMap[\"error\"] = {\n          ...createEventData(instance),\n          // TODO: handle custom ios error codes\n          error: nativeEvent.error as SpeechRecognitionErrorCode,\n          message: nativeEvent.message,\n        };\n        listener.call(instance, clientEvent);\n      },\n    };\n  },\n  result: (instance, listener) => {\n    return {\n      eventName: \"result\",\n      nativeListener: (\n        nativeEvent: ExpoSpeechRecognitionNativeEventMap[\"result\"],\n      ) => {\n        if (!instance.interimResults && !nativeEvent.isFinal) {\n          return;\n        }\n        const alternatives = nativeEvent.results.map(\n          (result) =>\n            new ExpoSpeechRecognitionAlternative(\n              result.confidence,\n              result.transcript,\n            ),\n        );\n        const clientEvent: SpeechRecognitionEventMap[\"result\"] = {\n          ...createEventData(instance),\n          results: new ExpoSpeechRecognitionResultList([\n            new ExpoSpeechRecognitionResult(nativeEvent.isFinal, alternatives),\n          ]),\n        };\n        listener.call(instance, clientEvent);\n      },\n    };\n  },\n};\n\ntype SpeechListener<K extends keyof SpeechRecognitionEventMap> = (\n  this: SpeechRecognition,\n  ev: SpeechRecognitionEventMap[K],\n) => any;\n\n/** A compatibility wrapper that implements the web SpeechRecognition API for React Native. */\nexport class ExpoWebSpeechRecognition implements SpeechRecognition {\n  lang = \"en-US\";\n  grammars: SpeechGrammarList = new ExpoWebSpeechGrammarList();\n  maxAlternatives = 1;\n  continuous = false;\n\n  #interimResults = false;\n\n  get interimResults(): boolean {\n    return this.#interimResults;\n  }\n\n  set interimResults(interimResults: boolean) {\n    this.#interimResults = interimResults;\n    // Subscribe to native\n  }\n\n  // Extended properties\n\n  /** [EXTENDED, default: undefined] An array of strings that will be used to provide context to the speech recognition engine. */\n  contextualStrings?: string[] = undefined;\n  /** [EXTENDED, default: false] Whether the speech recognition engine should require the device to be on when the recognition starts. */\n  requiresOnDeviceRecognition = false;\n  /** [EXTENDED, default: false] Whether the speech recognition engine should add punctuation to the transcription. */\n  addsPunctuation = false;\n  /** [EXTENDED, default: undefined] Android-specific options to pass to the recognizer. */\n  androidIntentOptions: ExpoSpeechRecognitionOptions[\"androidIntentOptions\"];\n  /** [EXTENDED, default: undefined] Audio source options to pass to the recognizer. */\n  audioSource?: ExpoSpeechRecognitionOptions[\"audioSource\"];\n  /** [EXTENDED, default: undefined] Audio recording options to pass to the recognizer. */\n  recordingOptions?: ExpoSpeechRecognitionOptions[\"recordingOptions\"];\n  /** [EXTENDED, default: \"android.speech.action.RECOGNIZE_SPEECH\"] The kind of intent action */\n  androidIntent?: ExpoSpeechRecognitionOptions[\"androidIntent\"] = undefined;\n  /** [EXTENDED, default: undefined] The hint for the speech recognition task. */\n  iosTaskHint?: ExpoSpeechRecognitionOptions[\"iosTaskHint\"] = undefined;\n  /** [EXTENDED, default: undefined] The audio session category and options to use. */\n  iosCategory?: ExpoSpeechRecognitionOptions[\"iosCategory\"] = undefined;\n  /**\n   * [EXTENDED, default: undefined]\n   *\n   * The package name of the speech recognition service to use.\n   * If not provided, the default service will be used.\n   *\n   * Obtain the supported packages by running `ExpoSpeechRecognitionModule.getSpeechRecognitionServices()`\n   *\n   * e.g. com.samsung.android.bixby.agent\"\n   */\n  androidRecognitionServicePackage: ExpoSpeechRecognitionOptions[\"androidRecognitionServicePackage\"];\n\n  // keyed by listener function\n  #subscriptionMap: Map<SpeechListener<any>, EventSubscription[]> = new Map();\n\n  start() {\n    ExpoSpeechRecognitionModule.requestPermissionsAsync().then(() => {\n      // A result doesn't matter,\n      // the module will emit an error if permissions are not granted\n      ExpoSpeechRecognitionModule.start({\n        lang: this.lang,\n        interimResults: this.interimResults,\n        maxAlternatives: this.maxAlternatives,\n        contextualStrings: this.contextualStrings,\n        requiresOnDeviceRecognition: this.requiresOnDeviceRecognition,\n        addsPunctuation: this.addsPunctuation,\n        continuous: this.continuous,\n        recordingOptions: this.recordingOptions,\n        androidIntentOptions: this.androidIntentOptions,\n        androidRecognitionServicePackage: this.androidRecognitionServicePackage,\n        audioSource: this.audioSource,\n        androidIntent: this.androidIntent,\n        iosTaskHint: this.iosTaskHint,\n        iosCategory: this.iosCategory,\n      });\n    });\n  }\n  stop = ExpoSpeechRecognitionModule.stop;\n  abort = ExpoSpeechRecognitionModule.abort;\n\n  #onstart: SpeechListener<\"start\"> | null = null;\n  set onstart(listener: SpeechListener<\"start\"> | null) {\n    this._setListeners(\"start\", listener, this.#onstart);\n    this.#onstart = listener;\n  }\n  /** Fired when the speech recognition starts. */\n  get onstart() {\n    return this.#onstart;\n  }\n\n  #onend: SpeechListener<\"end\"> | null = null;\n  set onend(listener: SpeechListener<\"end\"> | null) {\n    this._setListeners(\n      \"end\",\n      (ev) => {\n        listener?.call(this, ev);\n      },\n      this.#onend,\n    );\n    this.#onend = listener;\n  }\n  /** Fired when the speech recognition service has disconnected. */\n  get onend() {\n    return this.#onend;\n  }\n\n  #onerror: SpeechListener<\"error\"> | null = null;\n  set onerror(listener: SpeechListener<\"error\"> | null) {\n    this._setListeners(\"error\", listener, this.#onerror);\n    this.#onerror = listener;\n  }\n  /** Fired when the speech recognition service encounters an error. */\n  get onerror() {\n    return this.#onerror;\n  }\n\n  _setListeners<K extends keyof SpeechRecognitionEventMap>(\n    key: K,\n    listenerFn: SpeechListener<K> | null,\n    existingListener: SpeechListener<K> | null,\n  ) {\n    if (existingListener) {\n      this.removeEventListener(key, existingListener);\n    }\n    if (listenerFn) {\n      this.addEventListener(key, listenerFn);\n    }\n  }\n\n  #onresult: SpeechListener<\"result\"> | null = null;\n  set onresult(listener: SpeechListener<\"result\"> | null) {\n    this._setListeners(\"result\", listener, this.#onresult);\n    this.#onresult = listener;\n  }\n  /** Fired when the speech recognition service returns a result —\n   *  a word or phrase has been positively recognized and this has been communicated back to the app. */\n  get onresult() {\n    return this.#onresult;\n  }\n\n  #onnomatch: SpeechListener<\"nomatch\"> | null = null;\n  set onnomatch(listener: SpeechListener<\"nomatch\"> | null) {\n    this._setListeners(\"nomatch\", listener, this.#onnomatch);\n    this.#onnomatch = listener;\n  }\n  /** Fired when the speech recognition service returns a final result with no significant recognition. */\n  get onnomatch() {\n    return this.#onnomatch;\n  }\n\n  #onspeechstart: SpeechListener<\"speechstart\"> | null = null;\n  set onspeechstart(listener: SpeechListener<\"speechstart\"> | null) {\n    this._setListeners(\"speechstart\", listener, this.#onspeechstart);\n    this.#onspeechstart = listener;\n  }\n  /** Fired when the speech recognition service returns a final result with no significant recognition. */\n  get onspeechstart() {\n    return this.#onspeechstart;\n  }\n\n  #onspeechend: SpeechListener<\"speechend\"> | null = null;\n  set onspeechend(listener: SpeechListener<\"speechend\"> | null) {\n    this._setListeners(\"speechend\", listener, this.#onspeechend);\n    this.#onspeechend = listener;\n  }\n  /** Fired when the speech recognition service returns a final result with no significant recognition. */\n  get onspeechend() {\n    return this.#onspeechend;\n  }\n\n  #onaudiostart: SpeechListener<\"audiostart\"> | null = null;\n  set onaudiostart(listener: SpeechListener<\"audiostart\"> | null) {\n    this._setListeners(\"audiostart\", listener, this.#onaudiostart);\n    this.#onaudiostart = listener;\n  }\n  /** Fired when the user agent has started to capture audio. */\n  get onaudiostart() {\n    return this.#onaudiostart;\n  }\n\n  #onaudioend: SpeechListener<\"audioend\"> | null = null;\n  set onaudioend(listener: SpeechListener<\"audioend\"> | null) {\n    this._setListeners(\"audioend\", listener, this.#onaudioend);\n    this.#onaudioend = listener;\n  }\n  /** Fired when the user agent has finished capturing audio. */\n  get onaudioend() {\n    return this.#onaudioend;\n  }\n\n  /** [TODO] */\n  onsoundend: ((this: SpeechRecognition, ev: Event) => any) | null = null;\n  /** [TODO] */\n  onsoundstart: ((this: SpeechRecognition, ev: Event) => any) | null = null;\n\n  addEventListener<K extends keyof SpeechRecognitionEventMap>(\n    type: K,\n    listener: SpeechListener<K>,\n    options?: boolean | AddEventListenerOptions,\n  ): void {\n    const once = typeof options === \"object\" && options.once;\n\n    // If the user opts in to only listening once,\n    // wrap the listener in a function that removes the listener\n    const wrappedListener = once\n      ? (((ev) => {\n          listener.call(this, ev);\n          // remove the listeners from the map\n          for (const sub of this.#subscriptionMap.get(listener) ?? []) {\n            sub.remove();\n          }\n          this.#subscriptionMap.delete(listener);\n        }) as SpeechListener<K>)\n      : listener;\n\n    // Enhance the native listener with any necessary polyfills\n    const enhancedEvent: NativeEventAndListener<K> =\n      WebListenerTransformers[type]?.(this, wrappedListener) ??\n      stubEvent(\n        type,\n        this,\n        wrappedListener as (this: SpeechRecognition, ev: Event) => unknown,\n      );\n\n    const subscription = ExpoSpeechRecognitionModule.addListener(\n      enhancedEvent.eventName,\n      // @ts-expect-error\n      enhancedEvent.nativeListener,\n    );\n\n    // Store the subscriptions so we can remove them later\n    // This is keyed by the listener function so we can remove all subscriptions for a given listener\n    this.#subscriptionMap.set(listener, [subscription]);\n  }\n\n  removeEventListener<K extends keyof SpeechRecognitionEventMap>(\n    type: K,\n    listener: (\n      this: SpeechRecognition,\n      ev: SpeechRecognitionEventMap[K],\n    ) => any,\n    options?: boolean | EventListenerOptions | undefined,\n  ): void {\n    const subscriptions = this.#subscriptionMap.get(listener);\n    if (subscriptions) {\n      for (const subscription of subscriptions) {\n        subscription.remove();\n      }\n      this.#subscriptionMap.delete(listener);\n    }\n  }\n\n  dispatchEvent(event: Event): boolean {\n    throw new Error(\"Method not implemented.\");\n  }\n}\n\n/**\n * This class is just a polyfill and does nothing on Android/iOS\n */\nexport class ExpoWebSpeechGrammarList implements SpeechGrammarList {\n  get length() {\n    return this.#grammars.length;\n  }\n  #grammars: ExpoWebSpeechGrammar[] = [];\n  [index: number]: SpeechGrammar; // Indexer property\n\n  addFromURI(src: string, weight?: number | undefined): void {\n    // todo\n  }\n\n  item(index: number): ExpoWebSpeechGrammar {\n    return this.#grammars[index];\n  }\n\n  addFromString = (grammar: string, weight?: number) => {\n    // TODO: parse grammar to html entities (data:application/xml,....)\n    this.#grammars.push(new ExpoWebSpeechGrammar(grammar, weight));\n    // Set key on this object for compatibility with web SpeechGrammarList API\n    this[this.length - 1] = this.#grammars[this.length - 1];\n  };\n}\n\nexport class ExpoWebSpeechGrammar implements SpeechGrammar {\n  src = \"\";\n  weight = 1;\n\n  constructor(src: string, weight?: number) {\n    this.src = src;\n    this.weight = weight ?? 1;\n  }\n}\n\nclass ExpoSpeechRecognitionResultList implements SpeechRecognitionResultList {\n  #results: ExpoSpeechRecognitionResult[] = [];\n\n  [Symbol.iterator](): ArrayIterator<ExpoSpeechRecognitionResult> {\n    return this.#results[\n      Symbol.iterator\n    ]() as ArrayIterator<ExpoSpeechRecognitionResult>;\n  }\n  length: number;\n  item(index: number): SpeechRecognitionResult {\n    return this.#results[index];\n  }\n  [index: number]: SpeechRecognitionResult;\n\n  constructor(results: ExpoSpeechRecognitionResult[]) {\n    this.#results = results;\n    this.length = results.length;\n    for (let i = 0; i < this.#results.length; i++) {\n      this[i] = this.#results[i];\n    }\n  }\n}\n\nclass ExpoSpeechRecognitionResult implements SpeechRecognitionResult {\n  #alternatives: ExpoSpeechRecognitionAlternative[] = [];\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/SpeechRecognitionResult/isFinal) */\n  readonly isFinal: boolean;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/SpeechRecognitionResult/length) */\n  length: number;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/SpeechRecognitionResult/item) */\n  item(index: number): SpeechRecognitionAlternative {\n    return this.#alternatives[index];\n  }\n  [index: number]: SpeechRecognitionAlternative;\n  [Symbol.iterator](): ArrayIterator<SpeechRecognitionAlternative> {\n    return this.#alternatives[\n      Symbol.iterator\n    ]() as ArrayIterator<SpeechRecognitionAlternative>;\n  }\n\n  constructor(\n    isFinal: boolean,\n    alternatives: ExpoSpeechRecognitionAlternative[],\n  ) {\n    this.isFinal = isFinal;\n    this.length = alternatives.length;\n    this.#alternatives = alternatives;\n    for (let i = 0; i < alternatives.length; i++) {\n      this[i] = alternatives[i];\n    }\n  }\n}\n\nclass ExpoSpeechRecognitionAlternative implements SpeechRecognitionAlternative {\n  confidence: number;\n  transcript: string;\n\n  constructor(confidence: number, transcript: string) {\n    this.confidence = confidence;\n    this.transcript = transcript;\n  }\n}\n"]}