{"version": 3, "file": "ExpoSpeechRecognitionModule.js", "sourceRoot": "", "sources": ["../src/ExpoSpeechRecognitionModule.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,MAAM,CAAC;AAI3C,kEAAkE;AAClE,4EAA4E;AAC5E,MAAM,CAAC,MAAM,2BAA2B,GACtC,mBAAmB,CAAkC,uBAAuB,CAAC,CAAC;AAEhF,MAAM,IAAI,GAAG,2BAA2B,CAAC,IAAI,CAAC;AAC9C,MAAM,KAAK,GAAG,2BAA2B,CAAC,KAAK,CAAC;AAEhD,2BAA2B,CAAC,KAAK,GAAG,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;AAClD,2BAA2B,CAAC,IAAI,GAAG,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC", "sourcesContent": ["import { requireNativeModule } from \"expo\";\n\nimport type { ExpoSpeechRecognitionModuleType } from \"./ExpoSpeechRecognitionModule.types\";\n\n// It loads the native module object from the JSI or falls back to\n// the bridge module (from NativeModulesProxy) if the remote debugger is on.\nexport const ExpoSpeechRecognitionModule =\n  requireNativeModule<ExpoSpeechRecognitionModuleType>(\"ExpoSpeechRecognition\");\n\nconst stop = ExpoSpeechRecognitionModule.stop;\nconst abort = ExpoSpeechRecognitionModule.abort;\n\nExpoSpeechRecognitionModule.abort = () => abort();\nExpoSpeechRecognitionModule.stop = () => stop();\n"]}