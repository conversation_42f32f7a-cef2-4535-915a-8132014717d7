import React, { useEffect, useRef, useState } from 'react';
import { TouchableOpacity, StyleSheet, View, Text, ActivityIndicator, Animated } from 'react-native';
import { useVoice } from '../context/VoiceContext';
import voiceService from '../services/voiceService';

const VoiceButton = () => {
  const { 
    status, 
    isListening, 
    startListening, 
    stopListening, 
    feedbackMessage,
    lastCommand,
    recognizedCommand,
    STATUS
  } = useVoice();

  const [serverStatus, setServerStatus] = useState('checking');

  // Check server status on component mount
  useEffect(() => {
    checkServerStatus();
  }, []);

  // Check server status periodically
  useEffect(() => {
    const interval = setInterval(checkServerStatus, 10000); // Check every 10 seconds
    return () => clearInterval(interval);
  }, []);

  // Function to check server status
  const checkServerStatus = async () => {
    try {
      setServerStatus('checking');
      await voiceService.checkServerAvailability();
      setServerStatus('online');
    } catch (error) {
      setServerStatus('offline');
    }
  };

  // Animation for the pulsing effect
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  
  // Start pulsing animation when listening
  useEffect(() => {
    let pulseAnimation;
    let scaleAnimation;
    
    if (status === STATUS.LISTENING) {
      // Pulse animation for the microphone button
      pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
      
      // Scale animation for touch feedback
      scaleAnimation = Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]);
      scaleAnimation.start();
    } else {
      pulseAnim.setValue(1);
      scaleAnim.setValue(1);
    }
    
    return () => {
      if (pulseAnimation) {
        pulseAnimation.stop();
      }
      if (scaleAnimation) {
        scaleAnimation.stop();
      }
    };
  }, [status, pulseAnim, scaleAnim]);

  const handlePress = () => {
    if (serverStatus === 'offline') {
      // If server is offline, try to reconnect
      checkServerStatus();
      return;
    }
    
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Determine button color based on status
  const getButtonColor = () => {
    if (serverStatus === 'offline') {
      return '#ff6b6b'; // Red for offline server
    }
    
    if (serverStatus === 'checking') {
      return '#ffcc5c'; // Yellow for checking server
    }
    
    switch (status) {
      case STATUS.LISTENING:
        return '#ff4444';
      case STATUS.PROCESSING:
        return '#ffbb33';
      case STATUS.SPEAKING:
        return '#00C851';
      default:
        return '#6200ee';
    }
  };

  // Determine button text based on status
  const getButtonText = () => {
    if (serverStatus === 'offline') {
      return 'Server Offline';
    }
    
    if (serverStatus === 'checking') {
      return 'Checking Server';
    }
    
    switch (status) {
      case STATUS.LISTENING:
        return 'Listening...';
      case STATUS.PROCESSING:
        return 'Processing...';
      case STATUS.SPEAKING:
        return 'Speaking...';
      default:
        return 'Voice Search';
    }
  };
  
  // Get icon based on status
  const getButtonIcon = () => {
    if (serverStatus === 'offline') {
      return '❌';
    }
    
    if (serverStatus === 'checking') {
      return '⏳';
    }
    
    switch (status) {
      case STATUS.LISTENING:
        return '🎤';
      case STATUS.PROCESSING:
        return '⏳';
      case STATUS.SPEAKING:
        return '🔊';
      default:
        return '🎤';
    }
  };

  // Get animation style based on status
  const getAnimationStyle = () => {
    const baseStyle = { transform: [{ scale: pulseAnim }] };
    
    if (status === STATUS.LISTENING) {
      return {
        ...baseStyle,
        shadowColor: '#ff4444',
        shadowOffset: { width: 0, height: 0 },
        shadowOpacity: 0.5,
        shadowRadius: 10,
        elevation: 8,
      };
    }
    
    return baseStyle;
  };

  return (
    <View style={styles.container}>
      {feedbackMessage ? (
        <Text style={styles.feedbackText}>{feedbackMessage}</Text>
      ) : null}
      
      {recognizedCommand ? (
        <Text style={styles.recognizedText}>{recognizedCommand}</Text>
      ) : null}
      
      <Animated.View style={getAnimationStyle()}>
      <TouchableOpacity
        style={[styles.button, { backgroundColor: getButtonColor() }]}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        {status === STATUS.PROCESSING || serverStatus === 'checking' ? (
          <ActivityIndicator color="#ffffff" size="small" />
        ) : (
            <View style={styles.buttonContent}>
              <Text style={styles.buttonIcon}>{getButtonIcon()}</Text>
              <Text style={styles.buttonText}>{getButtonText()}</Text>
            </View>
        )}
      </TouchableOpacity>
      </Animated.View>
      
      {serverStatus === 'offline' && (
        <Text style={styles.helpText}>
          Speech recognition server is offline.
          Tap to retry connection.
        </Text>
      )}
      
      {status === STATUS.IDLE && serverStatus === 'online' && (
        <Text style={styles.helpText}>
          Tap to search by voice
        </Text>
      )}
      
      {status === STATUS.LISTENING && (
        <Text style={styles.helpText}>
          Say a command like "search for shoes"
        </Text>
      )}
      
      {lastCommand && status === STATUS.IDLE && serverStatus === 'online' && (
        <View style={styles.lastCommandContainer}>
          <Text style={styles.lastCommandLabel}>Last search:</Text>
          <Text style={styles.lastCommandText}>{lastCommand}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  button: {
    width: 70,
    height: 70,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  buttonContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    fontSize: 24,
    marginBottom: 2,
  },
  buttonText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  feedbackText: {
    marginBottom: 10,
    fontSize: 14,
    color: '#333',
    textAlign: 'center',
    padding: 5,
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
    overflow: 'hidden',
    maxWidth: '80%',
  },
  recognizedText: {
    marginBottom: 10,
    fontSize: 14,
    color: '#6200ee',
    fontWeight: 'bold',
    textAlign: 'center',
  },
  helpText: {
    marginTop: 10,
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  lastCommandContainer: {
    marginTop: 15,
    padding: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    maxWidth: '90%',
    alignItems: 'center',
  },
  lastCommandLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 3,
  },
  lastCommandText: {
    fontSize: 14,
    color: '#333',
    fontWeight: 'bold',
  },
});

export default VoiceButton; 