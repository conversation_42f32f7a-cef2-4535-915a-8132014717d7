import React, { useEffect } from 'react';
import { TouchableOpacity, StyleSheet, View, Text, ActivityIndicator, Animated } from 'react-native';
import { useVoice } from '../context/VoiceContext';

const VoiceButton = () => {
  const { 
    status, 
    isListening, 
    startListening, 
    stopListening, 
    feedbackMessage,
    lastCommand,
    recognizedCommand,
    STATUS
  } = useVoice();

  // Animation for the pulsing effect
  const pulseAnim = React.useRef(new Animated.Value(1)).current;
  
  // Start pulsing animation when listening
  useEffect(() => {
    let pulseAnimation;
    
    if (status === STATUS.LISTENING) {
      pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.2,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
    } else {
      pulseAnim.setValue(1);
    }
    
    return () => {
      if (pulseAnimation) {
        pulseAnimation.stop();
      }
    };
  }, [status, pulseAnim]);

  const handlePress = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening();
    }
  };

  // Determine button color based on status
  const getButtonColor = () => {
    switch (status) {
      case STATUS.LISTENING:
        return '#ff4444';
      case STATUS.PROCESSING:
        return '#ffbb33';
      case STATUS.SPEAKING:
        return '#00C851';
      default:
        return '#6200ee';
    }
  };

  // Determine button text based on status
  const getButtonText = () => {
    switch (status) {
      case STATUS.LISTENING:
        return 'Listening...';
      case STATUS.PROCESSING:
        return 'Processing...';
      case STATUS.SPEAKING:
        return 'Speaking...';
      default:
        return 'Voice Command';
    }
  };
  
  // Get icon based on status
  const getButtonIcon = () => {
    switch (status) {
      case STATUS.LISTENING:
        return '🎤';
      case STATUS.PROCESSING:
        return '⏳';
      case STATUS.SPEAKING:
        return '🔊';
      default:
        return '🎤';
    }
  };

  return (
    <View style={styles.container}>
      {feedbackMessage ? (
        <Text style={styles.feedbackText}>{feedbackMessage}</Text>
      ) : null}
      
      {recognizedCommand ? (
        <Text style={styles.recognizedText}>{recognizedCommand}</Text>
      ) : null}
      
      <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
        <TouchableOpacity
          style={[styles.button, { backgroundColor: getButtonColor() }]}
          onPress={handlePress}
          activeOpacity={0.7}
        >
          {status === STATUS.PROCESSING ? (
            <ActivityIndicator color="#ffffff" size="small" />
          ) : (
            <View style={styles.buttonContent}>
              <Text style={styles.buttonIcon}>{getButtonIcon()}</Text>
              <Text style={styles.buttonText}>{getButtonText()}</Text>
            </View>
          )}
        </TouchableOpacity>
      </Animated.View>
      
      {status === STATUS.IDLE && (
        <Text style={styles.helpText}>
          Tap to use voice commands
        </Text>
      )}
      
      {status === STATUS.LISTENING && (
        <Text style={styles.helpText}>
          Say a command like "search for shoes"
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
  },
  button: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: '#6200ee',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
  },
  buttonContent: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    color: '#ffffff',
    fontWeight: 'bold',
    fontSize: 10,
    textAlign: 'center',
  },
  buttonIcon: {
    fontSize: 18,
    marginBottom: 2,
  },
  feedbackText: {
    marginBottom: 10,
    color: '#555',
    fontSize: 14,
    textAlign: 'center',
    maxWidth: 300,
  },
  recognizedText: {
    marginBottom: 10,
    color: '#007bff',
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  commandText: {
    marginBottom: 10,
    color: '#777',
    fontSize: 12,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  helpText: {
    marginTop: 8,
    color: '#888',
    fontSize: 12,
    textAlign: 'center',
  },
});

export default VoiceButton; 