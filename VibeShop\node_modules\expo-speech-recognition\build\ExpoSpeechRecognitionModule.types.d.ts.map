{"version": 3, "file": "ExpoSpeechRecognitionModule.types.d.ts", "sourceRoot": "", "sources": ["../src/ExpoSpeechRecognitionModule.types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,MAAM,CAAC;AAEzC,MAAM,MAAM,uCAAuC,GAAG,kBAAkB,GAAG;IACzE;;;;;;OAMG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;CACtB,CAAC;AAEF,OAAO,KAAK,EACV,oBAAoB,EACpB,sBAAsB,EACtB,6BAA6B,EAC7B,kBAAkB,EAClB,oCAAoC,EACpC,kCAAkC,EAClC,WAAW,EACZ,MAAM,aAAa,CAAC;AAErB,MAAM,MAAM,2BAA2B,GACrC,CAAC,OAAO,sBAAsB,CAAC,CAAC,MAAM,OAAO,sBAAsB,CAAC,CAAC;AAEvE,MAAM,MAAM,2BAA2B,GAAG;IACxC,UAAU,EAAE,MAAM,CAAC;IACnB;;OAEG;IACH,UAAU,EAAE,MAAM,CAAC;IACnB;;;;;;;;;;;;;;;OAeG;IACH,QAAQ,EAAE,kCAAkC,EAAE,CAAC;CAChD,CAAC;AAEF,MAAM,MAAM,kCAAkC,GAAG;IAC/C,sDAAsD;IACtD,eAAe,EAAE,MAAM,CAAC;IACxB,oDAAoD;IACpD,aAAa,EAAE,MAAM,CAAC;IACtB,6DAA6D;IAC7D,OAAO,EAAE,MAAM,CAAC;IAChB,qHAAqH;IACrH,UAAU,EAAE,MAAM,CAAC;CACpB,CAAC;AAEF,6EAA6E;AAC7E,MAAM,MAAM,gCAAgC,GAAG;IAC7C,OAAO,EAAE,OAAO,CAAC;IACjB,OAAO,EAAE,2BAA2B,EAAE,CAAC;CACxC,CAAC;AAEF,MAAM,MAAM,8BAA8B;AACxC,6DAA6D;AAC3D,SAAS;AACX,6BAA6B;GAC3B,eAAe;AACjB,kJAAkJ;GAChJ,aAAa;AACf,wDAAwD;GACtD,wBAAwB;AAC1B,4EAA4E;GAC1E,SAAS;AACX,oCAAoC;GAClC,WAAW;AACb,0EAA0E;GACxE,aAAa;AACf,iCAAiC;GAC/B,qBAAqB;AAEvB,6EAA6E;GAC3E,MAAM;AACR,4DAA4D;GAC1D,QAAQ;AACV,iCAAiC;GAC/B,gBAAgB;AAClB,8BAA8B;GAC5B,SAAS,CAAC;AAEd,MAAM,MAAM,+BAA+B,GAAG;IAC5C,KAAK,EAAE,8BAA8B,CAAC;IACtC,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAEF,MAAM,MAAM,sBAAsB,GAAG;IACnC,8EAA8E;IAC9E,gBAAgB,EAAE,MAAM,CAAC;IACzB;;;;;;;;OAQG;IACH,UAAU,EAAE,MAAM,CAAC;IACnB,+FAA+F;IAC/F,qBAAqB,EAAE,MAAM,EAAE,CAAC;CACjC,CAAC;AAEF;;GAEG;AACH,MAAM,MAAM,mCAAmC,GAAG;IAChD,MAAM,EAAE,gCAAgC,CAAC;IACzC,KAAK,EAAE,+BAA+B,CAAC;IACvC,KAAK,EAAE,IAAI,CAAC;IACZ,WAAW,EAAE,IAAI,CAAC;IAClB,SAAS,EAAE,IAAI,CAAC;IAChB,iEAAiE;IACjE,OAAO,EAAE,IAAI,CAAC;IACd,kCAAkC;IAClC,UAAU,EAAE;QACV;;;;;;;;WAQG;QACH,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;KACpB,CAAC;IACF,gCAAgC;IAChC,QAAQ,EAAE;QACR;;;;;;;WAOG;QACH,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;KACpB,CAAC;IACF,GAAG,EAAE,IAAI,CAAC;IACV,UAAU,EAAE,IAAI,CAAC;IACjB,QAAQ,EAAE,IAAI,CAAC;IACf,iBAAiB,EAAE,sBAAsB,CAAC;IAC1C,YAAY,EAAE;QACZ;;;;WAIG;QACH,KAAK,EAAE,MAAM,CAAC;KACf,CAAC;CACH,CAAC;AAEF,MAAM,MAAM,4BAA4B,GAAG;IACzC,gEAAgE;IAChE,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,2GAA2G;IAC3G,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,+EAA+E;IAC/E,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;;;;;OAMG;IACH,iBAAiB,CAAC,EAAE,MAAM,EAAE,CAAC;IAC7B;;;;;;;;;OASG;IACH,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB;;;OAGG;IACH,2BAA2B,CAAC,EAAE,OAAO,CAAC;IACtC;;;;;;;;OAQG;IACH,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B;;;;;;;OAOG;IACH,gCAAgC,CAAC,EAAE,MAAM,CAAC;IAC1C;;;;OAIG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAC,oBAAoB,CAAC,CAAC;IACrD;;;;OAIG;IACH,WAAW,CAAC,EAAE,kBAAkB,CAAC;IACjC;;OAEG;IACH,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;IACpC;;;;;;;;;;OAUG;IACH,aAAa,CAAC,EACV,wCAAwC,GACxC,+CAA+C,GAC/C,kCAAkC,CAAC;IAEvC;;;;;;OAMG;IACH,WAAW,CAAC,EAAE,gBAAgB,CAAC;IAE/B;;;;;;;;;;;;;;OAcG;IACH,WAAW,CAAC,EAAE,kBAAkB,CAAC;IAEjC;;OAEG;IACH,wBAAwB,CAAC,EAAE;QACzB;;;;WAIG;QACH,OAAO,CAAC,EAAE,OAAO,CAAC;QAClB;;;;;;WAMG;QACH,cAAc,CAAC,EAAE,MAAM,CAAC;KACzB,CAAC;IAEF;;;;;;OAMG;IACH,yBAAyB,CAAC,EAAE,OAAO,CAAC;CACrC,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG,CAAC,OAAO,WAAW,CAAC,CAAC,MAAM,OAAO,WAAW,CAAC,CAAC;AAE9E,MAAM,MAAM,gBAAgB,GAAG;IAC7B;;;;OAIG;IACH,OAAO,EAAE,OAAO,CAAC;IACjB;;OAEG;IACH,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;IACxB;;;;;;OAMG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;IAC1B;;;;OAIG;IACH,cAAc,CAAC,EACX,kBAAkB,GAClB,kBAAkB,GAClB,gBAAgB,GAChB,gBAAgB,CAAC;CACtB,CAAC;AAEF,MAAM,MAAM,kBAAkB,GAAG;IAC/B;;;;OAIG;IACH,GAAG,EAAE,MAAM,CAAC;IACZ;;;;OAIG;IACH,aAAa,CAAC,EAAE,MAAM,CAAC;IACvB;;;;OAIG;IACH,aAAa,CAAC,EAAE,yBAAyB,CAAC;IAC1C;;;;OAIG;IACH,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB;;;;;;;;OAQG;IACH,gBAAgB,CAAC,EAAE,MAAM,CAAC;CAC3B,CAAC;AAEF,MAAM,MAAM,yBAAyB,GACnC,CAAC,OAAO,oBAAoB,CAAC,CAAC,MAAM,OAAO,oBAAoB,CAAC,CAAC;AAEnE,MAAM,MAAM,oBAAoB,GAAG;IACjC;;;;OAIG;IACH,qBAAqB,EAAE,MAAM,CAAC;IAC9B;;;;;;OAMG;IACH,mCAAmC,EAAE,OAAO,CAAC;IAC7C;;;;OAIG;IACH,+BAA+B,EAAE,OAAO,CAAC;IACzC;;;;;;;;OAQG;IACH,4BAA4B,EAAE,CAAC,OAAO,oCAAoC,CAAC,CAAC,MAAM,OAAO,oCAAoC,CAAC,CAAC;IAC/H;;;;;;;OAOG;IACH,uBAAuB,EAAE,SAAS,GAAG,SAAS,CAAC;IAC/C;;;;OAIG;IACH,uCAAuC,EAAE,OAAO,CAAC;IACjD;;;;OAIG;IACH,0CAA0C,EAAE,MAAM,EAAE,CAAC;IACrD;;;;;;;;OAQG;IACH,oBAAoB,EAAE,CAAC,OAAO,kCAAkC,CAAC,CAAC,MAAM,OAAO,kCAAkC,CAAC,CAAC;IAEnH;;;;;;OAMG;IACH,uCAAuC,EAAE,MAAM,EAAE,CAAC;IAClD;;;;OAIG;IACH,yDAAyD,EAAE,MAAM,CAAC;IAClE;;;;;;OAMG;IACH,kCAAkC,EAAE,MAAM,CAAC;IAC3C;;;;;;OAMG;IACH,0BAA0B,EAAE,OAAO,CAAC;IACpC;;;;OAIG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;;;;;OAMG;IACH,oBAAoB,EAAE,OAAO,CAAC;IAC9B;;;;OAIG;IACH,YAAY,EAAE,MAAM,CAAC;IACrB;;;;OAIG;IACH,6BAA6B,EAAE,OAAO,CAAC;IACvC;;;;OAIG;IACH,yBAAyB,EAAE,OAAO,CAAC;IACnC;;;;OAIG;IACH,YAAY,EAAE,OAAO,CAAC;IACtB;;;;;;;;;;OAUG;IACH,uBAAuB,EACnB,mCAAmC,GACnC,0DAA0D,GAC1D,mEAAmE,CAAC;IACxE;;;;;;;;;OASG;IACH,iDAAiD,EAAE,MAAM,CAAC;IAC1D;;;;;;;;OAQG;IACH,wCAAwC,EAAE,MAAM,CAAC;IACjD;;;;;;;;;OASG;IACH,0DAA0D,EAAE,MAAM,CAAC;CACpE,CAAC;AAEF,MAAM,MAAM,iCAAiC,GAAG;KAC7C,CAAC,IAAI,MAAM,mCAAmC,GAAG,CAChD,KAAK,EAAE,mCAAmC,CAAC,CAAC,CAAC,KAC1C,IAAI;CACV,CAAC;AAEF,MAAM,CAAC,OAAO,OAAO,+BAAgC,SAAQ,YAAY,CAAC,iCAAiC,CAAC;IAC1G;;OAEG;IACH,KAAK,CAAC,OAAO,EAAE,4BAA4B,GAAG,IAAI;IAClD;;OAEG;IACH,IAAI,IAAI,IAAI;IACZ;;OAEG;IACH,KAAK,IAAI,IAAI;IACb;;;;;;;;OAQG;IACH,uBAAuB,IAAI,OAAO,CAAC,uCAAuC,CAAC;IAC3E;;;;OAIG;IACH,mBAAmB,IAAI,OAAO,CAAC,uCAAuC,CAAC;IACvE;;OAEG;IACH,6BAA6B,IAAI,OAAO,CAAC,kBAAkB,CAAC;IAC5D;;;;;OAKG;IACH,iCAAiC,IAAI,OAAO,CAAC,kBAAkB,CAAC;IAChE;;OAEG;IACH,mCAAmC,IAAI,OAAO,CAAC,uCAAuC,CAAC;IACvF;;;;;;OAMG;IACH,uCAAuC,IAAI,OAAO,CAAC,uCAAuC,CAAC;IAC3F;;;;;;;OAOG;IACH,mBAAmB,CAAC,OAAO,EAAE;QAC3B;;;;;;WAMG;QACH,gCAAgC,CAAC,EAAE,MAAM,CAAC;KAC3C,GAAG,OAAO,CAAC;QACV;;WAEG;QACH,OAAO,EAAE,MAAM,EAAE,CAAC;QAClB;;;;WAIG;QACH,gBAAgB,EAAE,MAAM,EAAE,CAAC;KAC5B,CAAC;IACF;;;;OAIG;IACH,4BAA4B,IAAI,MAAM,EAAE;IACxC;;;;OAIG;IACH,4BAA4B,IAAI;QAC9B,iFAAiF;QACjF,WAAW,EAAE,MAAM,CAAC;KACrB;IACD;;;;;;OAMG;IACH,mBAAmB,IAAI;QACrB,0FAA0F;QAC1F,WAAW,EAAE,MAAM,CAAC;KACrB;IACD;;OAEG;IACH,2BAA2B,IAAI,OAAO;IACtC;;;;OAIG;IACH,iBAAiB,IAAI,OAAO;IAC5B;;;;OAIG;IACH,sBAAsB,IAAI,OAAO;IAEjC;;;OAGG;IACH,kCAAkC,CAAC,OAAO,EAAE;QAC1C,yDAAyD;QACzD,MAAM,EAAE,MAAM,CAAC;KAChB,GAAG,OAAO,CAAC;QACV;;;;WAIG;QACH,MAAM,EAAE,kBAAkB,GAAG,eAAe,GAAG,mBAAmB,CAAC;QACnE,OAAO,EAAE,MAAM,CAAC;KACjB,CAAC;IACF;;;;OAIG;IACH,cAAc,CAAC,OAAO,EAAE,kBAAkB,GAAG,IAAI;IAEjD;;;;;;;OAOG;IACH,oCAAoC,IAAI;QACtC,QAAQ,EAAE,2BAA2B,CAAC;QACtC,eAAe,EAAE,kCAAkC,EAAE,CAAC;QACtD,IAAI,EAAE,uBAAuB,CAAC;KAC/B;IACD;;;;;;OAMG;IACH,wBAAwB,CACtB,KAAK,EAAE,OAAO,EACd,OAAO,CAAC,EAAE;QACR,4FAA4F;QAC5F,0BAA0B,EAAE,OAAO,CAAC;KACrC,GACA,IAAI;IACP;;OAEG;IACH,aAAa,IAAI,OAAO,CAAC,sBAAsB,CAAC;CACjD;AAED,MAAM,MAAM,kBAAkB,GAAG;IAC/B,QAAQ,EAAE,2BAA2B,CAAC;IACtC,eAAe,EAAE,kCAAkC,EAAE,CAAC;IACtD,IAAI,CAAC,EAAE,uBAAuB,CAAC;CAChC,CAAC;AAEF,KAAK,sBAAsB,GACvB,UAAU,GACV,UAAU,GACV,aAAa,GACb,UAAU,CAAC;AAEf;;;;GAIG;AACH,MAAM,MAAM,kCAAkC,GAC5C,CAAC,OAAO,6BAA6B,CAAC,CAAC,MAAM,OAAO,6BAA6B,CAAC,CAAC;AAErF;;;;GAIG;AACH,MAAM,MAAM,uBAAuB,GACjC,CAAC,OAAO,kBAAkB,CAAC,CAAC,MAAM,OAAO,kBAAkB,CAAC,CAAC"}