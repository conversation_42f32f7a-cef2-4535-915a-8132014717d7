I want to build a voice-activated shopping app MVP using Expo (React Native) for iOS and Android.

The user flow should be entirely voice-controlled, from:

Voice command → product search

Product selection → voice confirmation

Voice confirmation → trigger Paystack payment

I don’t need advanced features like multilingual support or personalized recommendations yet.

Requirements:

✅ Built with Expo (managed workflow is preferred)

✅ Capture voice input

✅ Convert to text using Whisper (via Python backend) or Google Speech-to-Text

✅ Use the transcribed voice command to search for a product from a Firebase Firestore collection

✅ Let user confirm order via voice (e.g., “Yes, buy this”)

✅ Integrate Paystack Ghana API for payment (with fallback to button if voice is too hard)

Tech Stack:

Frontend: React Native (Expo)

Backend: Firebase + Python API (for Whisper)

Payments: Paystack Ghana