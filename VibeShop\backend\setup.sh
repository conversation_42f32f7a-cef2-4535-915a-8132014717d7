#!/bin/bash
echo "Setting up VibeShop Voice Recognition Backend..."

# Create virtual environment
echo "Creating virtual environment..."
python3 -m venv venv

# Activate virtual environment
echo "Activating virtual environment..."
source venv/bin/activate

# Install dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

echo "Setup complete!"
echo
echo "To run the server:"
echo "1. Activate the virtual environment: source venv/bin/activate"
echo "2. Run the server: python run.py" 