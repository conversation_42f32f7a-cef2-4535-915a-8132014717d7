import AsyncStorage from '@react-native-async-storage/async-storage';
import { firestore } from '../config/firebase';
import { collection, addDoc, doc, updateDoc, serverTimestamp } from 'firebase/firestore';

const CART_STORAGE_KEY = '@vibeshop_cart';
const ordersCollection = 'orders';

class CartService {
  constructor() {
    this.cart = {
      items: [],
      totalItems: 0,
      totalAmount: 0
    };
    this.loadCart();
  }

  // Load cart from AsyncStorage
  async loadCart() {
    try {
      const cartData = await AsyncStorage.getItem(CART_STORAGE_KEY);
      if (cartData) {
        this.cart = JSON.parse(cartData);
      }
      return this.cart;
    } catch (error) {
      console.error('Error loading cart:', error);
      return this.cart;
    }
  }

  // Save cart to AsyncStorage
  async saveCart() {
    try {
      await AsyncStorage.setItem(CART_STORAGE_KEY, JSON.stringify(this.cart));
    } catch (error) {
      console.error('Error saving cart:', error);
    }
  }

  // Get current cart
  async getCart() {
    await this.loadCart();
    return this.cart;
  }

  // Add item to cart
  async addItem(product, quantity = 1) {
    await this.loadCart();
    
    // Check if item already exists in cart
    const existingItemIndex = this.cart.items.findIndex(item => item.id === product.id);
    
    if (existingItemIndex >= 0) {
      // Update quantity if item exists
      this.cart.items[existingItemIndex].quantity += quantity;
    } else {
      // Add new item if it doesn't exist
      this.cart.items.push({
        id: product.id,
        name: product.name,
        price: product.price,
        imageUrl: product.imageUrl,
        quantity
      });
    }
    
    // Update cart totals
    this._updateCartTotals();
    
    // Save updated cart
    await this.saveCart();
    
    return this.cart;
  }

  // Remove item from cart
  async removeItem(productId) {
    await this.loadCart();
    
    // Filter out the item to remove
    this.cart.items = this.cart.items.filter(item => item.id !== productId);
    
    // Update cart totals
    this._updateCartTotals();
    
    // Save updated cart
    await this.saveCart();
    
    return this.cart;
  }

  // Update item quantity
  async updateItemQuantity(productId, quantity) {
    await this.loadCart();
    
    // Find the item to update
    const itemIndex = this.cart.items.findIndex(item => item.id === productId);
    
    if (itemIndex >= 0) {
      if (quantity <= 0) {
        // Remove item if quantity is 0 or negative
        return this.removeItem(productId);
      }
      
      // Update quantity
      this.cart.items[itemIndex].quantity = quantity;
      
      // Update cart totals
      this._updateCartTotals();
      
      // Save updated cart
      await this.saveCart();
    }
    
    return this.cart;
  }

  // Clear cart
  async clearCart() {
    this.cart = {
      items: [],
      totalItems: 0,
      totalAmount: 0
    };
    
    await this.saveCart();
    
    return this.cart;
  }

  // Create an order from the cart
  async createOrder(userInfo) {
    try {
      await this.loadCart();
      
      if (this.cart.items.length === 0) {
        throw new Error('Cannot create order with empty cart');
      }
      
      // Create order object
      const order = {
        items: this.cart.items,
        totalItems: this.cart.totalItems,
        totalAmount: this.cart.totalAmount,
        status: 'pending',
        createdAt: serverTimestamp(),
        userInfo
      };
      
      // Save order to Firestore
      const orderRef = await addDoc(collection(firestore, ordersCollection), order);
      
      // Clear cart after successful order creation
      await this.clearCart();
      
      return {
        orderId: orderRef.id,
        ...order,
        createdAt: new Date().toISOString() // Convert timestamp to string for immediate use
      };
    } catch (error) {
      console.error('Error creating order:', error);
      
      // For demo purposes, create a mock order if Firestore fails
      const mockOrderId = `ORDER-${Date.now()}`;
      const mockOrder = {
        orderId: mockOrderId,
        items: this.cart.items,
        totalItems: this.cart.totalItems,
        totalAmount: this.cart.totalAmount,
        status: 'pending',
        createdAt: new Date().toISOString(),
        userInfo
      };
      
      // Clear cart
      await this.clearCart();
      
      return mockOrder;
    }
  }

  // Helper method to update cart totals
  _updateCartTotals() {
    let totalItems = 0;
    let totalAmount = 0;
    
    this.cart.items.forEach(item => {
      totalItems += item.quantity;
      totalAmount += item.price * item.quantity;
    });
    
    this.cart.totalItems = totalItems;
    this.cart.totalAmount = totalAmount;
  }
}

export default new CartService(); 