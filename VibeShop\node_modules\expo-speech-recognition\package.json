{"name": "expo-speech-recognition", "version": "2.1.1", "description": "Speech Recognition for React Native Expo projects", "main": "build/index.js", "types": "build/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "expo-module build", "clean": "expo-module clean", "lint": "expo-module lint", "test": "expo-module test", "prepare": "expo-module prepare", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module", "open:ios": "open -a \"Xcode\" example/ios", "open:android": "open -a \"Android Studio\" example/android", "changeset": "changeset", "release": "npm run prepare && changeset publish", "ts:check": "tsc -p . --noEmit"}, "keywords": ["react-native", "expo", "expo-speech-recognition", "ExpoSpeechRecognition", "webkitSpeechRecognition", "SpeechRecognition"], "repository": "https://github.com/jamsch/expo-speech-recognition", "bugs": {"url": "https://github.com/jamsch/expo-speech-recognition/issues"}, "author": "jamsch (https://github.com/jamsch)", "license": "MIT", "homepage": "https://github.com/jamsch/expo-speech-recognition#readme", "devDependencies": {"@changesets/cli": "^2.29.2", "@types/dom-speech-recognition": "^0.0.6", "@types/react": "~19.0.14", "expo": "~53.0.0", "expo-module-scripts": "^4.1.6", "globals": "16.2.0", "typescript": "~5.8.3", "typescript-eslint": "8.33.1"}, "prettier": {"trailingComma": "all"}, "peerDependencies": {"expo": "*", "react": "*", "react-native": "*"}}