# VibeShop Voice Recognition Backend

This is the Python backend for the VibeShop voice recognition feature. It provides a REST API for speech-to-text conversion using Google's Speech Recognition.

## Setup

### Prerequisites

- Python 3.7 or higher
- pip (Python package manager)
- Virtual environment (recommended)

### Installation

1. Create a virtual environment (optional but recommended):

```bash
# On Windows
python -m venv venv
venv\Scripts\activate

# On macOS/Linux
python3 -m venv venv
source venv/bin/activate
```

2. Install dependencies:

```bash
pip install -r requirements.txt
```

## Running the Server

Start the server with:

```bash
python run.py
```

The server will run on http://localhost:5000 by default.

## API Endpoints

### Health Check

```
GET /health
```

Returns the status of the service.

### Speech Recognition

```
POST /recognize
```

Converts speech audio to text.

**Request:**
- Content-Type: multipart/form-data
- Parameters:
  - audio: Audio file (supported formats: wav, mp3, m4a)
  - language: Language code (default: en-US)

**Response:**
```json
{
  "success": true,
  "text": "recognized text",
  "language": "en-US"
}
```

### Available Commands

```
GET /commands
```

Returns a list of available voice commands for the app.

## Testing

You can test the API using curl:

```bash
curl -X POST -F "audio=@/path/to/audio/file.m4a" -F "language=en-US" http://localhost:5000/recognize
```

## Integration with React Native

The React Native app communicates with this backend to process voice commands. Make sure the server is running when testing voice features in the app. 