export declare const ExpoWebSpeechRecognition: {
    new (): SpeechRecognition;
    prototype: SpeechRecognition;
} | null;
export declare const ExpoWebSpeechGrammarList: {
    new (): SpeechGrammarList;
    prototype: SpeechGrammarList;
} | null;
export declare const ExpoWebSpeechRecognitionEvent: {
    new (type: string, eventInitDict: SpeechRecognitionEventInit): SpeechRecognitionEvent;
    prototype: SpeechRecognitionEvent;
} | null;
//# sourceMappingURL=ExpoWebSpeechRecognition.web.d.ts.map