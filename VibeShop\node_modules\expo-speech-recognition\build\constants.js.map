{"version": 3, "file": "constants.js", "sourceRoot": "", "sources": ["../src/constants.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG;IACpC;;;;;;;;OAQG;IACH,OAAO,EAAE,SAAS;IAClB;;;;;;;;OAQG;IACH,WAAW,EAAE,aAAa;IAC1B;;;;;;;;OAQG;IACH,QAAQ,EAAE,UAAU;IACpB;;;;;;;;;;OAUG;IACH,MAAM,EAAE,QAAQ;IAChB;;;;;;;;;;;;;;OAcG;IACH,aAAa,EAAE,eAAe;IAC9B;;;;;;;;OAQG;IACH,UAAU,EAAE,YAAY;CAChB,CAAC;AAEX;;;;GAIG;AACH,MAAM,CAAC,MAAM,6BAA6B,GAAG;IAC3C;;;;OAIG;IACH,aAAa,EAAE,eAAe;IAC9B;;;;OAIG;IACH,UAAU,EAAE,YAAY;IACxB;;;;OAIG;IACH,oCAAoC,EAAE,sCAAsC;IAC5E;;;;OAIG;IACH,cAAc,EAAE,gBAAgB;IAChC;;;;OAIG;IACH,kBAAkB,EAAE,oBAAoB;IACxC;;;;OAIG;IACH,YAAY,EAAE,cAAc;IAC5B;;;;OAIG;IACH,gBAAgB,EAAE,kBAAkB;IACpC;;;;OAIG;IACH,mCAAmC,EAAE,qCAAqC;CAClE,CAAC;AAEX;;;;GAIG;AACH,MAAM,CAAC,MAAM,kBAAkB,GAAG;IAChC;;;;OAIG;IACH,OAAO,EAAE,SAAS;IAClB;;;;;;OAMG;IACH,QAAQ,EAAE,UAAU;IACpB;;;;;;;;OAQG;IACH,WAAW,EAAE,aAAa;IAC1B;;;;;;OAMG;IACH,aAAa,EAAE,eAAe;IAC9B;;;;;;OAMG;IACH,WAAW,EAAE,aAAa;IAC1B;;;;;;;;OAQG;IACH,SAAS,EAAE,WAAW;IACtB;;;;;;OAMG;IACH,cAAc,EAAE,gBAAgB;IAChC;;;;;;;;OAQG;IACH,SAAS,EAAE,WAAW;IACtB;;;;;;;;OAQG;IACH,WAAW,EAAE,aAAa;CAClB,CAAC;AAEX;;;;GAIG;AACH,MAAM,CAAC,MAAM,kCAAkC,GAAG;IAChD;;;;OAIG;IACH,wBAAwB,EAAE,WAAW;IACrC;;;;OAIG;IACH,yBAAyB,EAAE,YAAY;CAC/B,CAAC;AAEX;;;;GAIG;AACH,MAAM,CAAC,MAAM,oCAAoC,GAAG;IAClD;;;;OAIG;IACH,wBAAwB,EAAE,UAAU;IACpC;;;;OAIG;IACH,8BAA8B,EAAE,gBAAgB;IAChD;;;;;;OAMG;IACH,8BAA8B,EAAE,gBAAgB;CACxC,CAAC;AAEX;;;;GAIG;AACH,MAAM,CAAC,MAAM,oBAAoB,GAAG;IAClC;;OAEG;IACH,YAAY,EAAE,CAAC;IACf;;OAEG;IACH,oBAAoB,EAAE,EAAE;IACxB;;OAEG;IACH,oBAAoB,EAAE,EAAE;IACxB;;OAEG;IACH,oBAAoB,EAAE,EAAE;IACxB;;OAEG;IACH,oBAAoB,EAAE,EAAE;IACxB;;OAEG;IACH,aAAa,EAAE,EAAE;IACjB;;OAEG;IACH,kBAAkB,EAAE,CAAC;IACrB;;OAEG;IACH,yBAAyB,EAAE,EAAE;IAC7B;;OAEG;IACH,kBAAkB,EAAE,EAAE;IACtB;;OAEG;IACH,iBAAiB,EAAE,CAAC;IACpB;;OAEG;IACH,kBAAkB,EAAE,CAAC;CACb,CAAC;AAEX;;;;GAIG;AACH,MAAM,CAAC,MAAM,WAAW,GAAG;IACzB;;;;OAIG;IACH,WAAW,EAAE,aAAa;IAC1B;;;;OAIG;IACH,SAAS,EAAE,WAAW;IACtB;;;;OAIG;IACH,MAAM,EAAE,QAAQ;IAChB;;;;OAIG;IACH,YAAY,EAAE,cAAc;CACpB,CAAC", "sourcesContent": ["/**\n * [iOS] Audio category used for defining the audio behavior\n *\n * Docs: [AVAudioSession.Category](https://developer.apple.com/documentation/avfaudio/avaudiosession/category)\n */\nexport const AVAudioSessionCategory = {\n  /**\n   * The category for an app in which sound playback is nonprimary — that is, your app also works with the sound turned off.\n   *\n   * This category is also appropriate for “play-along” apps, such as a virtual piano that a user plays while the Music app is playing.\n   * When you use this category, audio from other apps mixes with your audio.\n   * Screen locking and the Silent switch (on iPhone, the Ring/Silent switch) silence your audio.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/category/1616560-ambient\n   */\n  ambient: \"ambient\",\n  /**\n   * The default audio session category.\n   *\n   * Your audio is silenced by screen locking and by the Silent switch (called the Ring/Silent switch on iPhone).\n   *\n   * By default, using this category implies that your app’s audio is nonmixable—activating your session will interrupt any other audio sessions which are also nonmixable. To allow mixing, use the ambient category instead.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/category/1616488-soloambient\n   */\n  soloAmbient: \"soloAmbient\",\n  /**\n   * The category for playing recorded music or other sounds that are central to the successful use of your app.\n   *\n   * When using this category, your app audio continues with the Silent switch set to silent or when the screen locks. (The switch is called the Ring/Silent switch on iPhone.) To continue playing audio when your app transitions to the background (for example, when the screen locks), add the audio value to the UIBackgroundModes key in your information property list file.\n   *\n   * By default, using this category implies that your app’s audio is nonmixable—activating your session will interrupt any other audio sessions which are also nonmixable. To allow mixing for this category, use the mixWithOthers option.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/category/1616509-playback\n   */\n  playback: \"playback\",\n  /**\n   * The category for recording audio while also silencing playback audio.\n   *\n   * This category has the effect of silencing virtually all output on the system, for as long as the session is active. Unless you need to prevent any unexpected sounds from being played, use playAndRecord instead.\n   *\n   * To continue recording audio when your app transitions to the background (for example, when the screen locks), add the audio value to the UIBackgroundModes key in your information property list file.\n   *\n   * The user must grant permission for audio recording.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/category/1616451-record\n   */\n  record: \"record\",\n  /**\n   * The category for recording (input) and playback (output) of audio, such as for a Voice over Internet Protocol (VoIP) app.\n   *\n   * Your audio continues with the Silent switch set to silent and with the screen locked. (The switch is called the Ring/Silent switch on iPhone.) To continue playing audio when your app transitions to the background (for example, when the screen locks), add the audio value to the UIBackgroundModes key in your information property list file.\n   *\n   * This category is appropriate for simultaneous recording and playback, and also for apps that record and play back, but not simultaneously.\n   *\n   * By default, using this category implies that your app’s audio is nonmixable—activating your session will interrupt any other audio sessions which are also nonmixable. To allow mixing for this category, use the mixWithOthers option.\n   *\n   * The user must grant permission for audio recording.\n   *\n   * This category supports the mirrored version of AirPlay. However, AirPlay mirroring will be disabled if the AVAudioSessionModeVoiceChat mode is used with this category.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/category/1616568-playandrecord\n   */\n  playAndRecord: \"playAndRecord\",\n  /**\n   * The category for routing distinct streams of audio data to different output devices at the same time.\n   *\n   * This category can be used for input, output, or both. For example, use this category to route audio to\n   * both a USB device and a set of headphones. Use of this category requires a more detailed knowledge of,\n   * and interaction with, the capabilities of the available audio routes.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/category/1616484-multiroute\n   */\n  multiRoute: \"multiRoute\",\n} as const;\n\n/**\n * Constants that specify optional audio behaviors.\n *\n * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions\n */\nexport const AVAudioSessionCategoryOptions = {\n  /**\n   * An option that indicates whether audio from this session mixes with audio from active sessions in other audio apps.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions/1616611-mixwithothers\n   */\n  mixWithOthers: \"mixWithOthers\",\n  /**\n   * An option that reduces the volume of other audio sessions while audio from this session plays.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions/1616618-duckothers\n   */\n  duckOthers: \"duckOthers\",\n  /**\n   * An option that determines whether to pause spoken audio content from other sessions when your app plays its audio.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions/1616534-interruptspokenaudioandmixwithot\n   */\n  interruptSpokenAudioAndMixWithOthers: \"interruptSpokenAudioAndMixWithOthers\",\n  /**\n   * An option that determines whether Bluetooth hands-free devices appear as available input routes.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions/1616518-allowbluetooth\n   */\n  allowBluetooth: \"allowBluetooth\",\n  /**\n   * An option that determines whether you can stream audio from this session to Bluetooth devices that support the Advanced Audio Distribution Profile (A2DP).\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions/1771735-allowbluetootha2dp\n   */\n  allowBluetoothA2DP: \"allowBluetoothA2DP\",\n  /**\n   * An option that determines whether you can stream audio from this session to AirPlay devices.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions/1771736-allowairplay\n   */\n  allowAirPlay: \"allowAirPlay\",\n  /**\n   * An option that determines whether audio from the session defaults to the built-in speaker instead of the receiver.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions/3727255-overridemutedmicrophoneinterrupt\n   */\n  defaultToSpeaker: \"defaultToSpeaker\",\n  /**\n   * An option that indicates whether the system interrupts the audio session when it mutes the built-in microphone.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/categoryoptions/3727255-overridemutedmicrophoneinterrupt\n   */\n  overrideMutedMicrophoneInterruption: \"overrideMutedMicrophoneInterruption\",\n} as const;\n\n/**\n * The audio session mode, together with the audio session category, indicates to the system how you intend to use audio in your app. You can use a mode to configure the audio system for specific use cases such as video recording, voice or video chat, or audio analysis.\n *\n * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/1616508-mode\n */\nexport const AVAudioSessionMode = {\n  /**\n   * The default audio session mode. You can use this mode with every audio session category.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/mode/1616579-default\n   */\n  default: \"default\",\n  /**\n   * A mode that the GameKit framework sets on behalf of an application that uses GameKit’s voice chat service. This mode is valid only with the playAndRecord audio session category.\n   *\n   * Don’t set this mode directly. If you need similar behavior and aren’t using a GKVoiceChat object, use voiceChat or videoChat instead.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/mode/1616511-gamechat\n   */\n  gameChat: \"gameChat\",\n  /**\n   * A mode that indicates that your app is performing measurement of audio input or output.\n   *\n   * Use this mode for apps that need to minimize the amount of system-supplied signal processing to input and output signals. If recording on devices with more than one built-in microphone, the session uses the primary microphone.\n   *\n   * Important: This mode disables some dynamics processing on input and output signals, resulting in a lower-output playback level.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/mode/1616608-measurement\n   */\n  measurement: \"measurement\",\n  /**\n   * A mode that indicates that your app is playing back movie content.\n   *\n   * When you set this mode, the audio session uses signal processing to enhance movie playback for certain audio routes such as built-in speaker or headphones. You may only use this mode with the `playback` audio session category.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/mode/1616623-movieplayback\n   */\n  moviePlayback: \"moviePlayback\",\n  /**\n   * A mode used for continuous spoken audio to pause the audio when another app plays a short audio prompt.\n   *\n   * This mode is appropriate for apps that play continuous spoken audio, such as podcasts or audio books. Setting this mode indicates that your app should pause, rather than duck, its audio if another app plays a spoken audio prompt. After the interrupting app’s audio ends, you can resume your app’s audio playback.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/mode/1616510-spokenaudio\n   */\n  spokenAudio: \"spokenAudio\",\n  /**\n   * A mode that indicates that your app is engaging in online video conferencing.\n   *\n   * Use this mode for video chat apps that use the `playAndRecord` or `record` categories. When you set this mode, the audio session optimizes the device’s tonal equalization for voice. It also reduces the set of allowable audio routes to only those appropriate for video chat.\n   *\n   * Using this mode has the side effect of enabling the `allowBluetooth` category option.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/mode/1616590-videochat\n   */\n  videoChat: \"videoChat\",\n  /**\n   * A mode that indicates that your app is recording a movie.\n   *\n   * This mode is valid only with the `record` and `playAndRecord` audio session categories. On devices with more than one built-in microphone, the audio session uses the microphone closest to the video camera.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/mode/1616535-videorecording\n   */\n  videoRecording: \"videoRecording\",\n  /**\n   * A mode that indicates that your app is performing two-way voice communication, such as using Voice over Internet Protocol (VoIP).\n   *\n   * Use this mode for Voice over IP (VoIP) apps that use the `playAndRecord` category. When you set this mode, the session optimizes the device’s tonal equalization for voice and reduces the set of allowable audio routes to only those appropriate for voice chat.\n   *\n   * Using this mode has the side effect of enabling the `allowBluetooth` category option.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/mode/1616455-voicechat\n   */\n  voiceChat: \"voiceChat\",\n  /**\n   * A mode that indicates that your app plays audio using text-to-speech.\n   *\n   * Setting this mode allows for different routing behaviors when your app connects to certain audio devices, such as CarPlay. An example of an app that uses this mode is a turn-by-turn navigation app that plays short prompts to the user.\n   *\n   * Typically, apps of the same type also configure their sessions to use the duckOthers and interruptSpokenAudioAndMixWithOthers options.\n   *\n   * Docs: https://developer.apple.com/documentation/avfaudio/avaudiosession/mode/2962803-voiceprompt\n   */\n  voicePrompt: \"voicePrompt\",\n} as const;\n\n/**\n * Options for the `EXTRA_LANGUAGE_MODEL` extra.\n *\n * Docs: https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_LANGUAGE_MODEL\n */\nexport const RecognizerIntentExtraLanguageModel = {\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#LANGUAGE_MODEL_FREE_FORM\n   *\n   * Use a language model based on free-form speech recognition. This is a value to use for EXTRA_LANGUAGE_MODEL.\n   */\n  LANGUAGE_MODEL_FREE_FORM: \"free_form\",\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#LANGUAGE_MODEL_WEB_SEARCH\n   *\n   * Use a language model based on web search terms. This is a value to use for EXTRA_LANGUAGE_MODEL.\n   */\n  LANGUAGE_MODEL_WEB_SEARCH: \"web_search\",\n} as const;\n\n/**\n * Options for the `EXTRA_ENABLE_LANGUAGE_SWITCH` extra.\n *\n * Docs: https://developer.android.com/reference/android/speech/RecognizerIntent#EXTRA_LANGUAGE_SWITCH_MODEL\n */\nexport const RecognizerIntentEnableLanguageSwitch = {\n  /**\n   * A value to use for `EXTRA_ENABLE_LANGUAGE_SWITCH`.\n   *\n   * Enables language switch only when a new language is detected as at least [SpeechRecognizer#LANGUAGE_DETECTION_CONFIDENCE_LEVEL_CONFIDENT](https://developer.android.com/reference/android/speech/SpeechRecognizer#LANGUAGE_DETECTION_CONFIDENCE_LEVEL_CONFIDENT), which means the service is balancing between detecting a new language confidently and switching early.\n   */\n  LANGUAGE_SWITCH_BALANCED: \"balanced\",\n  /**\n   * A value to use for `EXTRA_ENABLE_LANGUAGE_SWITCH`.\n   *\n   * Enables language switch only when a new language is detected as [SpeechRecognizer#LANGUAGE_DETECTION_CONFIDENCE_LEVEL_HIGHLY_CONFIDENT](https://developer.android.com/reference/android/speech/SpeechRecognizer#LANGUAGE_DETECTION_CONFIDENCE_LEVEL_HIGHLY_CONFIDENT), which means the service may wait for longer before switching.\n   */\n  LANGUAGE_SWITCH_HIGH_PRECISION: \"high_precision\",\n  /**\n   * https://developer.android.com/reference/android/speech/RecognizerIntent#LANGUAGE_SWITCH_QUICK_RESPONSE\n   *\n   * A value to use for `EXTRA_ENABLE_LANGUAGE_SWITCH`.\n   *\n   * Enables language switch only when a new language is detected as at least [SpeechRecognizer#LANGUAGE_DETECTION_CONFIDENCE_LEVEL_NOT_CONFIDENT](https://developer.android.com/reference/android/speech/SpeechRecognizer#LANGUAGE_DETECTION_CONFIDENCE_LEVEL_NOT_CONFIDENT), which means the service should switch at the earliest moment possible.\n   */\n  LANGUAGE_SWITCH_QUICK_RESPONSE: \"quick_response\",\n} as const;\n\n/**\n * Android only\n *\n * See: [AudioFormat](https://developer.android.com/reference/android/media/AudioFormat)\n */\nexport const AudioEncodingAndroid = {\n  /**\n   * Audio data format: MP3 compressed\n   */\n  ENCODING_MP3: 9,\n  /**\n   * Audio data format: MPEG-H baseline profile, level 3\n   */\n  ENCODING_MPEGH_BL_L3: 23,\n  /**\n   * Audio data format: MPEG-H baseline profile, level 4\n   */\n  ENCODING_MPEGH_BL_L4: 24,\n  /**\n   * Audio data format: MPEG-H low complexity profile, level 3\n   */\n  ENCODING_MPEGH_LC_L3: 25,\n  /**\n   * Audio data format: MPEG-H low complexity profile, level 4\n   */\n  ENCODING_MPEGH_LC_L4: 26,\n  /**\n   * Audio data format: OPUS compressed.\n   */\n  ENCODING_OPUS: 20,\n  /**\n   * Audio data format: PCM 16 bit per sample. Guaranteed to be supported by devices.\n   */\n  ENCODING_PCM_16BIT: 2,\n  /**\n   * Audio data format: PCM 24 bit per sample packed as 3 bytes. The bytes are in little-endian order, so the least significant byte comes first in the byte array. Not guaranteed to be supported by devices, may be emulated if not supported.\n   */\n  ENCODING_PCM_24BIT_PACKED: 21,\n  /**\n   * Audio data format: PCM 32 bit per sample. Not guaranteed to be supported by devices, may be emulated if not supported.\n   */\n  ENCODING_PCM_32BIT: 22,\n  /**\n   * Audio data format: PCM 8 bit per sample. Not guaranteed to be supported by devices.\n   */\n  ENCODING_PCM_8BIT: 3,\n  /**\n   * Audio data format: single-precision floating-point per sample\n   */\n  ENCODING_PCM_FLOAT: 4,\n} as const;\n\n/**\n * [iOS Only] The type of task for which you are using speech recognition.\n *\n * Docs: https://developer.apple.com/documentation/speech/sfspeechrecognitiontaskhint\n */\nexport const TaskHintIOS = {\n  /**\n   * The task hint is unspecified.\n   *\n   * Use this hint type when the intended use for captured speech does not match the other task types.\n   */\n  unspecified: \"unspecified\",\n  /**\n   * A task that uses captured speech for text entry.\n   *\n   * Use this hint type when you are using speech recognition for a task that's similar to the keyboard's built-in dictation function.\n   */\n  dictation: \"dictation\",\n  /**\n   * A task that uses captured speech to specify seach terms.\n   *\n   * Use this hint type when you are using speech recognition to identify search terms.\n   */\n  search: \"search\",\n  /**\n   * A task that uses captured speech for short, confirmation-style requests.\n   *\n   * Use this hint type when you are using speech recognition to handle confirmation commands, such as \"yes,\" \"no,\" or \"maybe.\"\n   */\n  confirmation: \"confirmation\",\n} as const;\n"]}