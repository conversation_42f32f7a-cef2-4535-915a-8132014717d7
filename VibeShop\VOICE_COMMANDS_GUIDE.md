# VibeShop Voice Commands Guide

This guide provides detailed information on how to use voice commands effectively in the VibeShop application.

## Getting Started with Voice Commands

1. **Activate Voice Recognition**: Tap the microphone button at the bottom of the screen.
2. **Speak Clearly**: When the button turns purple, speak your command clearly.
3. **Wait for Processing**: The app will process your command and respond accordingly.

## Available Voice Commands

### Navigation Commands

| Command | Description | Example |
|---------|-------------|---------|
| **"Go home"** | Return to the home screen | "Go home" |
| **"Go back"** | Return to the previous screen | "Go back" |
| **"Help"** | Show available commands | "Help" |

### Search Commands

| Command | Description | Example |
|---------|-------------|---------|
| **"Search for [product]"** | Search for products | "Search for running shoes" |
| **"Find [product]"** | Alternative search command | "Find black t-shirts" |
| **"Show me [product]"** | Alternative search command | "Show me wireless headphones" |

### Product Selection Commands

| Command | Description | Example |
|---------|-------------|---------|
| **"Select item [number]"** | Select a product from search results | "Select item three" |
| **"Choose [number]"** | Alternative selection command | "Choose two" |
| **"View details of [number]"** | View product details | "View details of item five" |

### Shopping Cart Commands

| Command | Description | Example |
|---------|-------------|---------|
| **"Add to cart"** | Add the current product to your cart | "Add to cart" |
| **"View cart"** | Open your shopping cart | "View cart" |
| **"Remove item [number]"** | Remove an item from the cart | "Remove item two" |
| **"Clear cart"** | Remove all items from the cart | "Clear cart" |

### Checkout Commands

| Command | Description | Example |
|---------|-------------|---------|
| **"Checkout"** | Proceed to checkout | "Checkout" |
| **"Place order"** | Complete the purchase | "Place order" |

## Tips for Effective Voice Commands

1. **Speak Naturally**: The voice recognition system understands natural language.
2. **Wait for the Signal**: Make sure the microphone button turns purple before speaking.
3. **Use Numbers Clearly**: When selecting items, say the number clearly ("one", "two", "three", etc.).
4. **Check Network Connection**: Ensure you have a stable internet connection for optimal voice recognition.
5. **Quiet Environment**: Use voice commands in a relatively quiet environment for best results.

## Troubleshooting

### If the app doesn't recognize your command:

1. **Try Again**: Tap the microphone button and speak more clearly.
2. **Rephrase**: Try using different words or a simpler command.
3. **Check Connection**: Ensure your device is connected to the internet.
4. **Server Status**: The app will show a red indicator if the speech recognition server is unavailable.

### Common Issues:

- **Yellow Loading Indicator Stays On**: The app is having trouble connecting to the speech recognition server.
- **Red Indicator**: The speech recognition server is unavailable.
- **No Response After Speaking**: Your command may not have been heard clearly.

## Advanced Speech Recognition

The app uses two types of speech recognition:

1. **Standard Recognition**: Uses Google's speech recognition service (default).
2. **Advanced Recognition**: Uses on-device Wav2Vec2 model for better accuracy when available.

The app will automatically select the best available recognition method.
