# VibeShop Voice Recognition System

VibeShop is an e-commerce mobile application with advanced voice command capabilities. This repository contains both the React Native frontend and Python backend for speech recognition.

## System Architecture

The system consists of two main components:

1. **React Native Mobile App**: Handles UI, audio recording, and command processing
2. **Python Backend Server**: Provides advanced speech recognition capabilities

## Backend Setup

The backend uses Flask to serve a REST API for speech recognition.

### Requirements

- Python 3.8+
- PyTorch
- Transformers
- SpeechRecognition
- Flask

### Installation

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Install required packages:
   ```
   pip install -r requirements.txt
   ```

3. Run the server:
   ```
   python run.py
   ```

The server will start on port 5000 by default and will be accessible from other devices on your network.

### Configuration

- **PORT**: Set the `PORT` environment variable to change the default port (5000)
- **HOST**: Set the `HOST` environment variable to change the default host (0.0.0.0)

## Frontend Setup

### Requirements

- Node.js 14+
- Expo CLI
- React Native

### Installation

1. Install dependencies:
   ```
   npm install
   ```

2. Update the server IP in `src/config/api.js` to match your backend server's IP address.

3. Start the Expo development server:
   ```
   npm start
   ```

## Voice Commands

The application supports the following voice commands:

- **"Search for [product]"**: Search for products
- **"Select item [number]"**: Select a product from search results
- **"Add to cart"**: Add the current product to your cart
- **"View cart"**: Open your shopping cart
- **"Checkout"**: Proceed to checkout
- **"Go back"**: Return to the previous screen
- **"Go home"**: Return to the home screen
- **"Help"**: Show available commands

## Speech Recognition Models

The system supports two speech recognition models:

1. **Google Speech Recognition**: Default cloud-based recognition
2. **Wav2Vec2**: Advanced on-device recognition using PyTorch (when available)

## Troubleshooting

### Server Connection Issues

If the app shows a red indicator when trying to record:

1. Check that the backend server is running
2. Verify that the IP address in `src/config/api.js` matches your server's IP
3. Ensure your mobile device and server are on the same network

### Audio Recording Issues

If audio recording isn't working:

1. Check that your device has granted microphone permissions
2. Restart the app
3. Check the console logs for specific error messages
