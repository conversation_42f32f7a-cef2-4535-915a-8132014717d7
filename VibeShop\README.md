# VibeShop - Voice-Powered Shopping Experience

VibeShop is a React Native e-commerce application that allows users to complete their shopping journey using voice commands. This app demonstrates how voice technology can make online shopping more accessible and convenient.

## Features

- **Voice-Controlled Shopping**: Search, select, and purchase products using voice commands
- **Complete Shopping Flow**: Navigate through the entire shopping journey from product discovery to payment
- **Real Speech Recognition**: Uses device's built-in speech recognition for accurate command detection
- **Voice Feedback**: Provides audio feedback to guide users through the shopping process

## Voice Commands

VibeShop supports the following voice commands:

### Product Discovery
- "Search for shoes" - Search for shoes
- "Search for shirts" - Search for shirts
- "Search for pants" - Search for pants

### Product Selection
- "Select item one" - Select the first product
- "Select item two" - Select the second product
- "Select first item" - Select the first product

### Cart Management
- "Add to cart" - Add the current product to your cart
- "View cart" - Open your shopping cart
- "Remove from cart" - Remove the selected item from your cart
- "Clear cart" - Remove all items from your cart

### Checkout and Payment
- "Checkout" - Proceed to checkout
- "Confirm payment" - Complete your purchase

### Navigation
- "Go back" - Return to the previous screen
- "Go home" - Return to the home screen

### Help and Utilities
- "Help" - Show available commands
- "Cancel" - Cancel the current operation

## Implementation Details

### Voice Recognition System

The voice recognition system uses the device's built-in speech recognition capabilities:

1. **Speech Recognition**: Uses the @react-native-voice/voice package to access the device's native speech recognition
2. **Command Matching**: Matches recognized speech to the closest supported command
3. **Fallback System**: Uses audio analysis as a fallback when speech recognition is unavailable

### Voice Feedback

The app provides audio feedback using text-to-speech to guide users through the shopping process, making it more accessible and user-friendly.

## Getting Started

### Prerequisites

- Node.js (v14 or later)
- Expo CLI
- iOS device or simulator (for iOS speech recognition)
- Android device or emulator (for Android speech recognition)

### Installation

1. Clone the repository:
```
git clone https://github.com/yourusername/vibeshop.git
cd vibeshop
```

2. Install dependencies:
```
npm install
```

3. Start the development server:
```
npm start
```

4. Scan the QR code with the Expo Go app on your mobile device or use an emulator.

## Usage

1. Tap the voice button at the bottom of the screen to start listening
2. Speak a command clearly (e.g., "Search for shoes")
3. The app will recognize your command and provide audio feedback
4. Continue using voice commands to complete your shopping journey

## Technical Architecture

The voice shopping system consists of the following components:

- **VoiceButton.js**: UI component for initiating voice commands
- **VoiceContext.js**: Context provider for voice state management
- **voiceService.js**: Service for recording, processing, and synthesizing speech
- **commandProcessor.js**: Utility for parsing and interpreting voice commands

## License

This project is licensed under the MIT License - see the LICENSE file for details.
