{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAE5E,oCAAoC;AACpC,OAAO,EACL,wBAAwB,EACxB,oBAAoB,EACpB,wBAAwB,GACzB,MAAM,4BAA4B,CAAC;AAEpC,gBAAgB;AAChB,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAE5E,QAAQ;AACR,OAAO,EAAE,yBAAyB,EAAE,MAAM,6BAA6B,CAAC;AAExE,YAAY;AACZ,OAAO,EACL,sBAAsB,EACtB,6BAA6B,EAC7B,kBAAkB,EAClB,kCAAkC,EAClC,oCAAoC,EACpC,oBAAoB,EACpB,WAAW,GACZ,MAAM,aAAa,CAAC;AAErB,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,OAEnC,EAAE,EAAE,CAAC,2BAA2B,CAAC,mBAAmB,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC;AAErE,MAAM,CAAC,MAAM,4BAA4B,GACvC,2BAA2B,CAAC,4BAA4B,CAAC;AAE3D,MAAM,CAAC,MAAM,2BAA2B,GACtC,2BAA2B,CAAC,2BAA2B,CAAC;AAE1D,MAAM,CAAC,MAAM,iBAAiB,GAAG,2BAA2B,CAAC,iBAAiB,CAAC;AAE/E,MAAM,CAAC,MAAM,cAAc,GAAG,2BAA2B,CAAC,cAAc,CAAC;AAEzE,MAAM,CAAC,MAAM,oCAAoC,GAC/C,2BAA2B,CAAC,oCAAoC,CAAC;AAEnE,MAAM,CAAC,MAAM,wBAAwB,GACnC,2BAA2B,CAAC,wBAAwB,CAAC;AAEvD,MAAM,CAAC,MAAM,kCAAkC,GAC7C,2BAA2B,CAAC,kCAAkC,CAAC;AAEjE,MAAM,CAAC,MAAM,sBAAsB,GACjC,2BAA2B,CAAC,sBAAsB,CAAC;AAErD,MAAM,CAAC,MAAM,4BAA4B,GACvC,2BAA2B,CAAC,4BAA4B,CAAC;AAE3D,MAAM,CAAC,MAAM,mBAAmB,GAC9B,2BAA2B,CAAC,mBAAmB,CAAC;AAElD,MAAM,CAAC,MAAM,4BAA4B,GACvC,2BAA2B,CAAC,WAAW,CAAC", "sourcesContent": ["import { ExpoSpeechRecognitionModule } from \"./ExpoSpeechRecognitionModule\";\n\n// Export the SpeechRecognition APIs\nexport {\n  ExpoWebSpeechRecognition,\n  ExpoWebSpeechGrammar,\n  ExpoWebSpeechGrammarList,\n} from \"./ExpoWebSpeechRecognition\";\n\n// Native module\nexport { ExpoSpeechRecognitionModule } from \"./ExpoSpeechRecognitionModule\";\n\n// Hooks\nexport { useSpeechRecognitionEvent } from \"./useSpeechRecognitionEvent\";\n\n// Constants\nexport {\n  AVAudioSessionCategory,\n  AVAudioSessionCategoryOptions,\n  AVAudioSessionMode,\n  RecognizerIntentExtraLanguageModel,\n  RecognizerIntentEnableLanguageSwitch,\n  AudioEncodingAndroid,\n  TaskHintIOS,\n} from \"./constants\";\n\nexport const getSupportedLocales = (options?: {\n  androidRecognitionServicePackage?: string;\n}) => ExpoSpeechRecognitionModule.getSupportedLocales(options ?? {});\n\nexport const getSpeechRecognitionServices =\n  ExpoSpeechRecognitionModule.getSpeechRecognitionServices;\n\nexport const supportsOnDeviceRecognition =\n  ExpoSpeechRecognitionModule.supportsOnDeviceRecognition;\n\nexport const supportsRecording = ExpoSpeechRecognitionModule.supportsRecording;\n\nexport const setCategoryIOS = ExpoSpeechRecognitionModule.setCategoryIOS;\n\nexport const getAudioSessionCategoryAndOptionsIOS =\n  ExpoSpeechRecognitionModule.getAudioSessionCategoryAndOptionsIOS;\n\nexport const setAudioSessionActiveIOS =\n  ExpoSpeechRecognitionModule.setAudioSessionActiveIOS;\n\nexport const androidTriggerOfflineModelDownload =\n  ExpoSpeechRecognitionModule.androidTriggerOfflineModelDownload;\n\nexport const isRecognitionAvailable =\n  ExpoSpeechRecognitionModule.isRecognitionAvailable;\n\nexport const getDefaultRecognitionService =\n  ExpoSpeechRecognitionModule.getDefaultRecognitionService;\n\nexport const getAssistantService =\n  ExpoSpeechRecognitionModule.getAssistantService;\n\nexport const addSpeechRecognitionListener =\n  ExpoSpeechRecognitionModule.addListener;\n\nexport type {\n  ExpoSpeechRecognitionOptions,\n  AndroidIntentOptions,\n  ExpoSpeechRecognitionNativeEventMap,\n  AVAudioSessionCategoryOptionsValue,\n  AVAudioSessionModeValue,\n  AVAudioSessionCategoryValue,\n  AudioEncodingAndroidValue,\n  AudioSourceOptions,\n  RecordingOptions,\n  IOSTaskHintValue,\n  SetCategoryOptions,\n  ExpoSpeechRecognitionErrorCode,\n  ExpoSpeechRecognitionErrorEvent,\n  ExpoSpeechRecognitionResultEvent,\n  ExpoSpeechRecognitionResult,\n  ExpoSpeechRecognitionResultSegment,\n} from \"./ExpoSpeechRecognitionModule.types\";\n"]}