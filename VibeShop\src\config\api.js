/**
 * API configuration settings
 */

// Replace with your actual server IP address or hostname
// For local development with Expo, use your computer's local IP address
const SERVER_IP = '***************'; // Updated to match the actual server IP
const SERVER_PORT = 5000;

// API configuration
export const API_CONFIG = {
  baseUrl: `http://${SERVER_IP}:${SERVER_PORT}`,
  endpoints: {
    recognize: '/recognize',
    commands: '/commands',
    health: '/health'
  }
};

export default API_CONFIG; 