{"version": 3, "file": "useSpeechRecognitionEvent.js", "sourceRoot": "", "sources": ["../src/useSpeechRecognitionEvent.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,2BAA2B,EAAE,MAAM,+BAA+B,CAAC;AAE5E,OAAO,EAAE,gBAAgB,EAAE,MAAM,MAAM,CAAC;AAExC;;;;;;;GAOG;AACH,MAAM,UAAU,yBAAyB,CAEvC,SAAY,EAAE,QAA8C;IAC5D,OAAO,gBAAgB,CACrB,2BAA2B,EAC3B,SAAS;IACT,mBAAmB;IACnB,QAAQ,CACT,CAAC;AACJ,CAAC", "sourcesContent": ["import { ExpoSpeechRecognitionModule } from \"./ExpoSpeechRecognitionModule\";\nimport type { ExpoSpeechRecognitionNativeEvents } from \"./ExpoSpeechRecognitionModule.types\";\nimport { useEventListener } from \"expo\";\n\n/**\n * This hook allows you to listen to native events emitted by the `ExpoSpeechRecognitionModule`.\n *\n * Note: this is not the same as the `SpeechRecognition` event listener on the web speech API.\n *\n * @param eventName The name of the event to listen to\n * @param listener The listener function to call when the event is emitted\n */\nexport function useSpeechRecognitionEvent<\n  K extends keyof ExpoSpeechRecognitionNativeEvents,\n>(eventName: K, listener: ExpoSpeechRecognitionNativeEvents[K]) {\n  return useEventListener(\n    ExpoSpeechRecognitionModule,\n    eventName,\n    // @ts-expect-error\n    listener,\n  );\n}\n"]}