#!/usr/bin/env python
"""
Run script for the VibeShop voice recognition backend server.
"""
import os
import logging
from app import create_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Create the Flask app
app = create_app()

if __name__ == '__main__':
    # Get port from environment variable or use default
    port = int(os.environ.get('PORT', 5000))
    
    # Get host from environment variable or use default
    # Use 0.0.0.0 to make the server accessible from other devices on the network
    host = os.environ.get('HOST', '0.0.0.0')
    
    logger.info(f"Starting voice recognition server on {host}:{port}")
    
    # Run the app
    app.run(host=host, port=port, debug=True) 