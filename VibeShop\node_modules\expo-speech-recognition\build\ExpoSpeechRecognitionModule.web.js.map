{"version": 3, "file": "ExpoSpeechRecognitionModule.web.js", "sourceRoot": "", "sources": ["../src/ExpoSpeechRecognitionModule.web.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE,MAAM,MAAM,CAAC;AAQvD,IAAI,qBAAqB,GAA6B,IAAI,CAAC;AAM3D,kEAAkE;AAClE,4EAA4E;AAC5E,MAAM,8BAA+B,SAAQ,YAA+C;IAC1F,gBAAgB,GAKZ,IAAI,GAAG,EAAE,CAAC;IAEd,gBAAgB,GAA0C,IAAI,GAAG,EAAE,CAAC;IAEpE,4CAA4C;IAC5C,iBAAiB,GAAG,CAClB,SAAY,EACZ,EAAgC,EAChC,EAAE;QACF,MAAM,YAAY,GAAG,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAE1D,IAAI,CAAC,IAAI,CACP,SAAS;QACT,iDAAiD;QACjD,YAAY,CACb,CAAC;IACJ,CAAC,CAAC;IAEF,WAAW,CACT,SAAoB,EACpB,QAAsD;QAEtD,4CAA4C;QAE5C,mDAAmD;QACnD,MAAM,cAAc,GAAG,CAAC,EAAwC,EAAE,EAAE;YAClE,MAAM,OAAO,GACX,SAAS,IAAI,mBAAmB;gBAC9B,CAAC,CAAC,mBAAmB,CAAC,SAA4C,CAAC;gBACnE,CAAC,CAAC,IAAI,CAAC;YAEX,iDAAiD;YACjD,MAAM,YAAY,GAAG,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YAEnC,mBAAmB;YACnB,QAAQ,CAAC,YAAY,CAAC,CAAC;QACzB,CAAC,CAAC;QAEF,mBAAmB;QACnB,qBAAqB,EAAE,gBAAgB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACnE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YAC1C,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QAClD,CAAC;QACD,sDAAsD;QACtD,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC,cAAc,CAAC,CAAC;QAC1D,qDAAqD;QACrD,mBAAmB;QACnB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEpD,MAAM,MAAM,GAAG,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAEtD,OAAO;YACL,MAAM,EAAE,GAAG,EAAE;gBACX,mBAAmB;gBACnB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,cAAc,CAAC,CAAC;gBAC7D,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACvC,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,CAAC;SACF,CAAC;IACJ,CAAC;IAED,kBAAkB,CAChB,SAAoD;QAEpD,gEAAgE;QAChE,IAAI,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;YACzC,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC7D,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO;YACT,CAAC;YAED,gCAAgC;YAChC,KAAK,MAAM,CAAC,QAAQ,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBAC/D,4CAA4C;gBAC5C,IAAI,eAAe,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;oBACxC,+BAA+B;oBAC/B,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;YAED,8BAA8B;YAC9B,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE,CAAC;gBACvC,mBAAmB;gBACnB,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC3C,CAAC;YAED,WAAW;YACX,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAqC;QACzC,MAAM,sBAAsB,GAC1B,OAAO,uBAAuB,KAAK,WAAW;YAC5C,CAAC,CAAC,uBAAuB;YACzB,CAAC,CAAC,iBAAiB,CAAC;QACxB,qBAAqB,GAAG,IAAI,sBAAsB,EAAE,CAAC;QACrD,qBAAqB,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,OAAO,CAAC;QACrD,qBAAqB,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,KAAK,CAAC;QACvE,qBAAqB,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,IAAI,CAAC,CAAC;QACrE,qBAAqB,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC;QAE/D,oDAAoD;QACpD,oGAAoG;QACpG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE;YACrD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBACjC,4BAA4B;gBAC5B,qBAAqB,EAAE,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAChE,qBAAqB,EAAE,gBAAgB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,gCAAgC;QAChC,qBAAqB,CAAC,KAAK,EAAE,CAAC;IAChC,CAAC;IAED,aAAa;QACX,OAAO,CAAC,IAAI,CACV,8DAA8D,CAC/D,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACrC,CAAC;IAED,IAAI;QACF,qBAAqB,EAAE,IAAI,EAAE,CAAC;IAChC,CAAC;IACD,KAAK;QACH,qBAAqB,EAAE,KAAK,EAAE,CAAC;IACjC,CAAC;IAED,uBAAuB;QACrB,OAAO,CAAC,IAAI,CACV,2FAA2F,CAC5F,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,SAAS;SACI,CAAC,CAAC;IAC3B,CAAC;IAED,mBAAmB;QACjB,OAAO,CAAC,IAAI,CACV,uFAAuF,CACxF,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,SAAS;SACI,CAAC,CAAC;IAC3B,CAAC;IAED,6BAA6B;QAC3B,OAAO,CAAC,IAAI,CACV,iGAAiG,CAClG,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,SAAS;SACI,CAAC,CAAC;IAC3B,CAAC;IAED,iCAAiC;QAC/B,OAAO,CAAC,IAAI,CACV,qGAAqG,CACtG,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,SAAS;SACI,CAAC,CAAC;IAC3B,CAAC;IAED,mCAAmC;QACjC,OAAO,CAAC,IAAI,CACV,uGAAuG,CACxG,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,SAAS;SACI,CAAC,CAAC;IAC3B,CAAC;IAED,uCAAuC;QACrC,OAAO,CAAC,IAAI,CACV,2GAA2G,CAC5G,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,OAAO,EAAE,IAAI;YACb,WAAW,EAAE,KAAK;YAClB,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,SAAS;SACI,CAAC,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,OAAO,CAAC,IAAI,CACV,wEAAwE,CACzE,CAAC;QACF,OAAO;YACL,OAAO,EAAE,EAAc;YACvB,gBAAgB,EAAE,EAAc;SACjC,CAAC;IACJ,CAAC;IAED,mBAAmB;IAEnB,uBAAuB;IAEvB,4BAA4B;QAC1B,OAAO,EAAc,CAAC;IACxB,CAAC;IAED,4BAA4B;QAC1B,OAAO;YACL,WAAW,EAAE,EAAE;SAChB,CAAC;IACJ,CAAC;IAED,mBAAmB;QACjB,OAAO;YACL,WAAW,EAAE,EAAE;SAChB,CAAC;IACJ,CAAC;IAED,2BAA2B;QACzB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,iBAAiB;QACf,OAAO,KAAK,CAAC;IACf,CAAC;IAED,kCAAkC;QAChC,OAAO,CAAC,IAAI,CACV,8EAA8E,CAC/E,CAAC;QACF,OAAO,OAAO,CAAC,OAAO,CAAC;YACrB,MAAM,EAAE,eAAe;YACvB,OAAO,EAAE,iDAAiD;SAC3D,CAAC,CAAC;IACL,CAAC;IAED,cAAc;QACZ,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;IAC1D,CAAC;IAED,oCAAoC;QAClC,OAAO,CAAC,IAAI,CACV,+DAA+D,CAChE,CAAC;QACF,OAAO;YACL,QAAQ,EAAE,eAAe;YACzB,eAAe,EAAE,CAAC,kBAAkB,EAAE,gBAAgB,CAAC;YACvD,IAAI,EAAE,aAAa;SACpB,CAAC;IACJ,CAAC;IAED,wBAAwB;QACtB,OAAO,CAAC,IAAI,CAAC,mDAAmD,CAAC,CAAC;IACpE,CAAC;IAED,sBAAsB;QACpB,MAAM,uBAAuB,GAC3B,OAAO,uBAAuB,KAAK,WAAW;YAC9C,OAAO,iBAAiB,KAAK,WAAW,CAAC;QAE3C,OAAO,uBAAuB,CAAC;IACjC,CAAC;CACF;AAED;;GAEG;AACH,MAAM,mBAAmB,GAIrB;IACF,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IACjC,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;IACnC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI;IACjB,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC;IACzD,OAAO,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI;IACrB,MAAM,EAAE,CAAC,EAAE,EAAiD,EAAE;QAC5D,MAAM,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,OAAO,CAAC,CAAC;QAE7D,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,OAAO,GACX,EAAE,CAAC;YAEL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3D,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,OAAO,CAAC,IAAI,CAAC;oBACX,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,QAAQ,EAAE,EAAE;iBACb,CAAC,CAAC;YACL,CAAC;YACD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO;aACR,CAAC;QACJ,CAAC;QAED,4CAA4C;QAC5C,IAAI,UAAU,GAAG,EAAE,CAAC;QACpB,MAAM,QAAQ,GAAyC,EAAE,CAAC;QAE1D,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACxD,MAAM,UAAU,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAEjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAC3C,MAAM,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC7B,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,SAAS;gBACX,CAAC;gBACD,QAAQ,CAAC,IAAI,CAAC;oBACZ,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,OAAO,EAAE,MAAM,CAAC,UAAU;oBAC1B,eAAe,EAAE,CAAC;oBAClB,aAAa,EAAE,CAAC;iBACjB,CAAC,CAAC;gBAEH,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,OAAO,EAAE,KAAK;YACd,OAAO,EAAE;gBACP;oBACE,UAAU;oBACV,UAAU,EACR,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;wBACxD,QAAQ,CAAC,MAAM;oBACjB,QAAQ;iBACT;aACF;SACF,CAAC;IACJ,CAAC;IACD,UAAU,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI;IACxB,SAAS,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI;IACvB,WAAW,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI;IACzB,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI;IACnB,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,IAAI;CACvB,CAAC;AAEF,MAAM,CAAC,MAAM,2BAA2B,GAAG,iBAAiB,CAC1D,8BAA8B,EAC9B,6BAA6B,CAC9B,CAAC", "sourcesContent": ["import type { EventSubscription, PermissionResponse } from \"expo-modules-core\";\nimport { registerWebModule, NativeModule } from \"expo\";\nimport type {\n  ExpoSpeechRecognitionNativeEventMap,\n  ExpoSpeechRecognitionNativeEvents,\n  ExpoSpeechRecognitionOptions,\n  ExpoSpeechRecognitionResultSegment,\n} from \"./ExpoSpeechRecognitionModule.types\";\n\nlet _speechRecognitionRef: SpeechRecognition | null = null;\n\ntype NativeEventListener = (\n  event: SpeechRecognitionEventMap[keyof SpeechRecognitionEventMap],\n) => void;\n\n// It loads the native module object from the JSI or falls back to\n// the bridge module (from NativeModulesProxy) if the remote debugger is on.\nclass ExpoSpeechRecognitionModuleWeb extends NativeModule<ExpoSpeechRecognitionNativeEvents> {\n  _clientListeners: Map<\n    // Original listener\n    ExpoSpeechRecognitionNativeEvents[keyof ExpoSpeechRecognitionNativeEvents],\n    // Native listener\n    NativeEventListener\n  > = new Map();\n\n  _nativeListeners: Map<string, Set<NativeEventListener>> = new Map();\n\n  // Convert the web event to the native event\n  bindEventListener = <T extends keyof SpeechRecognitionEventMap>(\n    eventName: T,\n    ev: SpeechRecognitionEventMap[T],\n  ) => {\n    const eventPayload = webToNativeEventMap[eventName]?.(ev);\n\n    this.emit(\n      eventName,\n      // @ts-expect-error payload typings are incorrect\n      eventPayload,\n    );\n  };\n\n  addListener<EventName extends keyof ExpoSpeechRecognitionNativeEventMap>(\n    eventName: EventName,\n    listener: ExpoSpeechRecognitionNativeEvents[EventName],\n  ): EventSubscription {\n    // Convert the web event to the native event\n\n    // @ts-expect-error Not all events are covered here\n    const nativeListener = (ev: SpeechRecognitionEventMap[EventName]) => {\n      const handler =\n        eventName in webToNativeEventMap\n          ? webToNativeEventMap[eventName as keyof SpeechRecognitionEventMap]\n          : null;\n\n      // @ts-expect-error payload typings are incorrect\n      const eventPayload = handler?.(ev);\n\n      // @ts-expect-error\n      listener(eventPayload);\n    };\n\n    // @ts-expect-error\n    _speechRecognitionRef?.addEventListener(eventName, nativeListener);\n    if (!this._nativeListeners.has(eventName)) {\n      this._nativeListeners.set(eventName, new Set());\n    }\n    // Add the original listener to the enhanced listeners\n    // @ts-expect-error\n    this._nativeListeners.get(eventName)?.add(nativeListener);\n    // Map the original listener to the enhanced listener\n    // @ts-expect-error\n    this._clientListeners.set(listener, nativeListener);\n\n    const handle = super.addListener(eventName, listener);\n\n    return {\n      remove: () => {\n        // @ts-expect-error\n        this._nativeListeners.get(eventName)?.delete(nativeListener);\n        this._clientListeners.delete(listener);\n        handle.remove();\n      },\n    };\n  }\n\n  removeAllListeners(\n    eventName: keyof ExpoSpeechRecognitionNativeEventMap,\n  ): void {\n    // Go through _listeners and remove all listeners for this event\n    if (this._nativeListeners.has(eventName)) {\n      const nativeListeners = this._nativeListeners.get(eventName);\n      if (!nativeListeners) {\n        return;\n      }\n\n      // Remove the enhanced listeners\n      for (const [listener, nativeListener] of this._clientListeners) {\n        // if nativeListener in listeners, remove it\n        if (nativeListeners.has(nativeListener)) {\n          // Remove the enhanced listener\n          this.removeListener(eventName, listener);\n        }\n      }\n\n      // ...and the native listeners\n      for (const listener of nativeListeners) {\n        // @ts-expect-error\n        this.removeListener(eventName, listener);\n      }\n\n      // Clean up\n      this._nativeListeners.delete(eventName);\n    }\n  }\n\n  start(options: ExpoSpeechRecognitionOptions) {\n    const SpeechRecognitionClass =\n      typeof webkitSpeechRecognition !== \"undefined\"\n        ? webkitSpeechRecognition\n        : SpeechRecognition;\n    _speechRecognitionRef = new SpeechRecognitionClass();\n    _speechRecognitionRef.lang = options.lang ?? \"en-US\";\n    _speechRecognitionRef.interimResults = options.interimResults ?? false;\n    _speechRecognitionRef.maxAlternatives = options.maxAlternatives ?? 1;\n    _speechRecognitionRef.continuous = options.continuous ?? false;\n\n    // Re-subscribe to events so that we don't lose them\n    // This covers the case where the user has already subscribed to an event prior to calling `start()`\n    this._nativeListeners.forEach((listeners, eventName) => {\n      for (const listener of listeners) {\n        // May already be subscribed\n        _speechRecognitionRef?.removeEventListener(eventName, listener);\n        _speechRecognitionRef?.addEventListener(eventName, listener);\n      }\n    });\n\n    // Start the speech recognition!\n    _speechRecognitionRef.start();\n  }\n\n  getStateAsync() {\n    console.warn(\n      \"getStateAsync is not supported on web. Returning 'inactive'.\",\n    );\n    return Promise.resolve(\"inactive\");\n  }\n\n  stop() {\n    _speechRecognitionRef?.stop();\n  }\n  abort() {\n    _speechRecognitionRef?.abort();\n  }\n\n  requestPermissionsAsync() {\n    console.warn(\n      \"requestPermissionsAsync is not supported on web. Returning a granted permission response.\",\n    );\n    return Promise.resolve({\n      granted: true,\n      canAskAgain: false,\n      expires: \"never\",\n      status: \"granted\",\n    } as PermissionResponse);\n  }\n\n  getPermissionsAsync() {\n    console.warn(\n      \"getPermissionsAsync is not supported on web. Returning a granted permission response.\",\n    );\n    return Promise.resolve({\n      granted: true,\n      canAskAgain: false,\n      expires: \"never\",\n      status: \"granted\",\n    } as PermissionResponse);\n  }\n\n  getMicrophonePermissionsAsync() {\n    console.warn(\n      \"getMicrophonePermissionsAsync is not supported on web. Returning a granted permission response.\",\n    );\n    return Promise.resolve({\n      granted: true,\n      canAskAgain: false,\n      expires: \"never\",\n      status: \"granted\",\n    } as PermissionResponse);\n  }\n\n  requestMicrophonePermissionsAsync() {\n    console.warn(\n      \"requestMicrophonePermissionsAsync is not supported on web. Returning a granted permission response.\",\n    );\n    return Promise.resolve({\n      granted: true,\n      canAskAgain: false,\n      expires: \"never\",\n      status: \"granted\",\n    } as PermissionResponse);\n  }\n\n  getSpeechRecognizerPermissionsAsync() {\n    console.warn(\n      \"getSpeechRecognizerPermissionsAsync is not supported on web. Returning a granted permission response.\",\n    );\n    return Promise.resolve({\n      granted: true,\n      canAskAgain: false,\n      expires: \"never\",\n      status: \"granted\",\n    } as PermissionResponse);\n  }\n\n  requestSpeechRecognizerPermissionsAsync() {\n    console.warn(\n      \"requestSpeechRecognizerPermissionsAsync is not supported on web. Returning a granted permission response.\",\n    );\n    return Promise.resolve({\n      granted: true,\n      canAskAgain: false,\n      expires: \"never\",\n      status: \"granted\",\n    } as PermissionResponse);\n  }\n\n  async getSupportedLocales() {\n    console.warn(\n      \"getSupportedLocales is not supported on web. Returning an empty array.\",\n    );\n    return {\n      locales: [] as string[],\n      installedLocales: [] as string[],\n    };\n  }\n\n  // addListener() {}\n\n  // removeListeners() {}\n\n  getSpeechRecognitionServices() {\n    return [] as string[];\n  }\n\n  getDefaultRecognitionService() {\n    return {\n      packageName: \"\",\n    };\n  }\n\n  getAssistantService() {\n    return {\n      packageName: \"\",\n    };\n  }\n\n  supportsOnDeviceRecognition() {\n    return false;\n  }\n\n  supportsRecording() {\n    return false;\n  }\n\n  androidTriggerOfflineModelDownload() {\n    console.warn(\n      \"androidTriggerOfflineModelDownload is not supported on web. Returning false.\",\n    );\n    return Promise.resolve({\n      status: \"opened_dialog\",\n      message: \"Offline model download is not supported on web.\",\n    });\n  }\n\n  setCategoryIOS() {\n    console.warn(\"setCategoryIOS is not supported on web.\");\n  }\n\n  getAudioSessionCategoryAndOptionsIOS() {\n    console.warn(\n      \"getAudioSessionCategoryAndOptionsIOS is not supported on web.\",\n    );\n    return {\n      category: \"playAndRecord\",\n      categoryOptions: [\"defaultToSpeaker\", \"allowBluetooth\"],\n      mode: \"measurement\",\n    };\n  }\n\n  setAudioSessionActiveIOS() {\n    console.warn(\"setAudioSessionActiveIOS is not supported on web.\");\n  }\n\n  isRecognitionAvailable() {\n    const hasSpeechRecognitionAPI =\n      typeof webkitSpeechRecognition !== \"undefined\" ||\n      typeof SpeechRecognition !== \"undefined\";\n\n    return hasSpeechRecognitionAPI;\n  }\n}\n\n/**\n * Convert the web SpeechRecognitionEventMap to the native event map for compatibility\n */\nconst webToNativeEventMap: {\n  [K in keyof SpeechRecognitionEventMap]: (\n    ev: SpeechRecognitionEventMap[K],\n  ) => ExpoSpeechRecognitionNativeEventMap[K];\n} = {\n  audioend: (ev) => ({ uri: null }),\n  audiostart: (ev) => ({ uri: null }),\n  end: (ev) => null,\n  error: (ev) => ({ error: ev.error, message: ev.message }),\n  nomatch: (ev) => null,\n  result: (ev): ExpoSpeechRecognitionNativeEventMap[\"result\"] => {\n    const isFinal = Boolean(ev.results[ev.resultIndex]?.isFinal);\n\n    if (isFinal) {\n      const results: ExpoSpeechRecognitionNativeEventMap[\"result\"][\"results\"] =\n        [];\n\n      for (let i = 0; i < ev.results[ev.resultIndex].length; i++) {\n        const result = ev.results[ev.resultIndex][i];\n        results.push({\n          transcript: result.transcript,\n          confidence: result.confidence,\n          segments: [],\n        });\n      }\n      return {\n        isFinal: true,\n        results,\n      };\n    }\n\n    // Interim results: Append to the transcript\n    let transcript = \"\";\n    const segments: ExpoSpeechRecognitionResultSegment[] = [];\n\n    for (let i = ev.resultIndex; i < ev.results.length; i++) {\n      const resultList = ev.results[i];\n\n      for (let j = 0; j < resultList.length; j++) {\n        const result = resultList[j];\n        if (!result) {\n          continue;\n        }\n        segments.push({\n          confidence: result.confidence,\n          segment: result.transcript,\n          startTimeMillis: 0,\n          endTimeMillis: 0,\n        });\n\n        if (!isFinal) {\n          transcript += result.transcript;\n        }\n      }\n    }\n\n    return {\n      isFinal: false,\n      results: [\n        {\n          transcript,\n          confidence:\n            segments.reduce((acc, curr) => acc + curr.confidence, 0) /\n            segments.length,\n          segments,\n        },\n      ],\n    };\n  },\n  soundstart: (ev) => null,\n  speechend: (ev) => null,\n  speechstart: (ev) => null,\n  start: (ev) => null,\n  soundend: (ev) => null,\n};\n\nexport const ExpoSpeechRecognitionModule = registerWebModule(\n  ExpoSpeechRecognitionModuleWeb,\n  \"ExpoSpeechRecognitionModule\",\n);\n"]}