import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  SafeAreaView, 
  StatusBar,
  TouchableOpacity,
  Image
} from 'react-native';
import ProductCard from '../components/ProductCard';
import VoiceButton from '../components/VoiceButton';
import productService from '../services/productService';
import { useVoice } from '../context/VoiceContext';

const HomeScreen = ({ navigation }) => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const { transcript, lastCommand, navigate } = useVoice();

  // Use the navigate function from VoiceContext if available, otherwise use the navigation prop
  const navigateTo = (screen, params) => {
    if (navigate) {
      navigate(screen, params);
    } else if (navigation) {
      navigation.navigate(screen, params);
    }
  };

  // Load featured products when component mounts
  useEffect(() => {
    const loadFeaturedProducts = async () => {
      try {
        setLoading(true);
        // Try to get products from Firestore, fallback to mock data
        try {
          const products = await productService.getFeaturedProducts();
          if (products && products.length > 0) {
            setFeaturedProducts(products);
          } else {
            throw new Error('No products found in Firestore');
          }
        } catch (error) {
          console.log('Falling back to mock products:', error);
          // Get mock products directly
          const mockProducts = productService.getMockProducts();
          const featured = mockProducts.filter(p => p.featured);
          console.log('Featured mock products:', featured);
          setFeaturedProducts(featured);
        }
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedProducts();
  }, []);

  // Handle voice commands
  useEffect(() => {
    if (lastCommand) {
      const commandLower = lastCommand.toLowerCase();
      
      // Handle search command
      if (commandLower.includes('search')) {
        const searchTerm = commandLower.replace('search for', '').replace('search', '').trim();
        if (searchTerm) {
          handleSearch(searchTerm);
        }
      }
      
      // Handle view cart command
      if (commandLower.includes('view cart') || commandLower.includes('view my cart')) {
        navigateTo('Cart');
      }
    }
  }, [lastCommand, navigation]);

  // Handle search
  const handleSearch = async (searchTerm) => {
    try {
      const products = await productService.getMockProducts(searchTerm);
      navigateTo('SearchResults', { searchTerm, products });
    } catch (error) {
      console.error('Error searching products:', error);
    }
  };

  // Handle product selection
  const handleProductPress = (product) => {
    navigateTo('ProductDetail', { 
      productId: product.id,
      productName: product.name,
      product
    });
  };

  // Handle cart navigation
  const handleCartPress = () => {
    navigateTo('Cart');
  };

  // Render header with welcome message and cart button
  const renderHeader = () => (
    <View style={styles.header}>
      <View>
        <Text style={styles.welcomeText}>Welcome to</Text>
        <Text style={styles.appName}>VibeShop</Text>
        <Text style={styles.subtitle}>Voice Shopping Assistant</Text>
      </View>
      <TouchableOpacity style={styles.cartButton} onPress={handleCartPress}>
        <Text style={styles.cartButtonText}>🛒</Text>
      </TouchableOpacity>
    </View>
  );

  // Render voice instructions
  const renderVoiceInstructions = () => (
    <View style={styles.voiceInstructionsContainer}>
      <Text style={styles.voiceInstructionsTitle}>Voice Commands</Text>
      <Text style={styles.voiceInstructionsText}>
        • "Search for shoes"
      </Text>
      <Text style={styles.voiceInstructionsText}>
        • "View my cart"
      </Text>
      <Text style={styles.voiceInstructionsText}>
        • "Checkout"
      </Text>
      {transcript ? (
        <View style={styles.transcriptContainer}>
          <Text style={styles.transcriptTitle}>Last command:</Text>
          <Text style={styles.transcriptText}>{transcript}</Text>
        </View>
      ) : null}
    </View>
  );

  // Render featured products section
  const renderFeaturedProducts = () => {
    console.log('Featured products count:', featuredProducts.length);
    return (
      <View style={styles.featuredContainer}>
        <Text style={styles.sectionTitle}>Featured Products</Text>
        {loading ? (
          <Text style={styles.loadingText}>Loading products...</Text>
        ) : featuredProducts && featuredProducts.length > 0 ? (
          <FlatList
            data={featuredProducts}
            renderItem={({ item }) => (
              <ProductCard product={item} onPress={handleProductPress} />
            )}
            keyExtractor={(item) => item.id.toString()}
            horizontal={false}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.productList}
          />
        ) : (
          <Text style={styles.noProductsText}>No featured products available</Text>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#6200ee" barStyle="light-content" />
      
      {renderHeader()}
      
      {renderVoiceInstructions()}
      
      {renderFeaturedProducts()}
      
      <View style={styles.voiceButtonContainer}>
        <VoiceButton />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10,
    backgroundColor: '#6200ee',
  },
  welcomeText: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.8,
  },
  appName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  subtitle: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.8,
  },
  cartButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartButtonText: {
    fontSize: 20,
  },
  voiceInstructionsContainer: {
    backgroundColor: '#fff',
    margin: 16,
    padding: 16,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  voiceInstructionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#6200ee',
  },
  voiceInstructionsText: {
    fontSize: 14,
    marginBottom: 5,
    color: '#333',
  },
  transcriptContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 5,
  },
  transcriptTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  transcriptText: {
    fontSize: 14,
    fontStyle: 'italic',
    color: '#6200ee',
  },
  featuredContainer: {
    flex: 1,
    padding: 16,
  },
  productList: {
    paddingBottom: 80, // Add padding to avoid the voice button
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  loadingText: {
    textAlign: 'center',
    marginTop: 20,
    color: '#666',
  },
  noProductsText: {
    textAlign: 'center',
    marginTop: 20,
    color: '#666',
  },
  voiceButtonContainer: {
    position: 'absolute',
    bottom: 20,
    right: 20,
  },
});

export default HomeScreen; 