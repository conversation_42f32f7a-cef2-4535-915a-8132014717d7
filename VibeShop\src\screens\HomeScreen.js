import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  FlatList, 
  SafeAreaView, 
  StatusBar,
  TouchableOpacity,
  Image,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import ProductCard from '../components/ProductCard';
import VoiceButton from '../components/VoiceButton';
import productService from '../services/productService';
import { useVoice } from '../context/VoiceContext';

const HomeScreen = ({ navigation }) => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [loading, setLoading] = useState(true);
  const { transcript, lastCommand, navigate } = useVoice();

  // Use the navigate function from VoiceContext if available, otherwise use the navigation prop
  const navigateTo = (screen, params) => {
    if (navigate) {
      navigate(screen, params);
    } else if (navigation) {
      navigation.navigate(screen, params);
    }
  };

  // Load featured products when component mounts
  useEffect(() => {
    const loadFeaturedProducts = async () => {
      try {
        setLoading(true);
        // Try to get products from Firestore, fallback to mock data
        try {
          const products = await productService.getFeaturedProducts();
          if (products && products.length > 0) {
            setFeaturedProducts(products);
          } else {
            throw new Error('No products found in Firestore');
          }
        } catch (error) {
          console.log('Falling back to mock products:', error);
          // Get mock products directly
          const mockProducts = productService.getMockProducts();
          const featured = mockProducts.filter(p => p.featured);
          console.log('Featured mock products:', featured);
          setFeaturedProducts(featured);
        }
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedProducts();
  }, []);

  // Handle voice commands
  useEffect(() => {
    if (lastCommand) {
      const commandLower = lastCommand.toLowerCase();
      
      // Handle search command
      if (commandLower.includes('search')) {
        const searchTerm = commandLower.replace('search for', '').replace('search', '').trim();
        if (searchTerm) {
          handleSearch(searchTerm);
          
          // Add to recent searches
          setRecentSearches(prev => {
            // Remove duplicates and limit to 5 recent searches
            const updatedSearches = [searchTerm, ...prev.filter(s => s !== searchTerm)].slice(0, 5);
            return updatedSearches;
          });
        }
      }
      
      // Handle view cart command
      if (commandLower.includes('view cart') || commandLower.includes('view my cart')) {
        navigateTo('Cart');
      }
    }
  }, [lastCommand, navigation]);

  // Handle search
  const handleSearch = async (searchTerm) => {
    try {
      const products = await productService.getMockProducts(searchTerm);
      navigateTo('SearchResults', { searchTerm, products });
    } catch (error) {
      console.error('Error searching products:', error);
    }
  };

  // Handle product selection
  const handleProductPress = (product) => {
    navigateTo('ProductDetail', { 
      productId: product.id,
      productName: product.name,
      product
    });
  };

  // Handle cart navigation
  const handleCartPress = () => {
    navigateTo('Cart');
  };

  // Handle recent search press
  const handleRecentSearchPress = (searchTerm) => {
    handleSearch(searchTerm);
  };

  // Render header with welcome message and cart button
  const renderHeader = () => (
    <View style={styles.header}>
      <View>
        <Text style={styles.welcomeText}>Welcome to</Text>
        <Text style={styles.appName}>VibeShop</Text>
        <Text style={styles.subtitle}>Voice Shopping Assistant</Text>
      </View>
      <TouchableOpacity style={styles.cartButton} onPress={handleCartPress}>
        <Text style={styles.cartButtonText}>🛒</Text>
      </TouchableOpacity>
    </View>
  );

  // Render voice instructions
  const renderVoiceInstructions = () => (
    <View style={styles.voiceInstructionsContainer}>
      <Text style={styles.voiceInstructionsTitle}>Voice Commands</Text>
      <View style={styles.commandsGrid}>
        <View style={styles.commandItem}>
          <Text style={styles.commandIcon}>🔍</Text>
          <Text style={styles.voiceInstructionsText}>
            "Search for shoes"
          </Text>
        </View>
        <View style={styles.commandItem}>
          <Text style={styles.commandIcon}>🛒</Text>
          <Text style={styles.voiceInstructionsText}>
            "View my cart"
          </Text>
        </View>
        <View style={styles.commandItem}>
          <Text style={styles.commandIcon}>💳</Text>
          <Text style={styles.voiceInstructionsText}>
            "Checkout"
          </Text>
        </View>
      </View>
      {transcript ? (
        <View style={styles.transcriptContainer}>
          <Text style={styles.transcriptTitle}>Last command:</Text>
          <Text style={styles.transcriptText}>{transcript}</Text>
        </View>
      ) : null}
    </View>
  );

  // Render recent searches
  const renderRecentSearches = () => {
    if (recentSearches.length === 0) return null;
    
    return (
      <View style={styles.recentSearchesContainer}>
        <Text style={styles.sectionTitle}>Recent Searches</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.recentSearchesScroll}>
          {recentSearches.map((search, index) => (
            <TouchableOpacity 
              key={index} 
              style={styles.recentSearchItem}
              onPress={() => handleRecentSearchPress(search)}
            >
              <Text style={styles.recentSearchIcon}>🔍</Text>
              <Text style={styles.recentSearchText}>{search}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  // Render featured products section
  const renderFeaturedProducts = () => {
    console.log('Featured products count:', featuredProducts.length);
    return (
      <View style={styles.featuredContainer}>
        <Text style={styles.sectionTitle}>Featured Products</Text>
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#6200ee" />
            <Text style={styles.loadingText}>Loading products...</Text>
          </View>
        ) : featuredProducts && featuredProducts.length > 0 ? (
          <FlatList
            data={featuredProducts}
            renderItem={({ item }) => (
              <ProductCard product={item} onPress={handleProductPress} />
            )}
            keyExtractor={(item) => item.id.toString()}
            horizontal={false}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={styles.productList}
          />
        ) : (
          <Text style={styles.noProductsText}>No featured products available</Text>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#6200ee" barStyle="light-content" />
      
      <ScrollView style={styles.scrollContainer}>
        {renderHeader()}
        
        {renderVoiceInstructions()}
        
        {renderRecentSearches()}
        
        {renderFeaturedProducts()}
      </ScrollView>
      
      <View style={styles.voiceButtonContainer}>
        <VoiceButton />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#6200ee',
  },
  welcomeText: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 16,
  },
  appName: {
    color: '#fff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 14,
    marginTop: 2,
  },
  cartButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartButtonText: {
    fontSize: 24,
  },
  voiceInstructionsContainer: {
    margin: 15,
    padding: 15,
    backgroundColor: '#f5f5f5',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 2,
  },
  voiceInstructionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  commandsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  commandItem: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '30%',
    marginBottom: 10,
  },
  commandIcon: {
    fontSize: 18,
    marginRight: 5,
  },
  voiceInstructionsText: {
    fontSize: 14,
    color: '#555',
    flex: 1,
  },
  transcriptContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: 'rgba(98, 0, 238, 0.1)',
    borderRadius: 5,
  },
  transcriptTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#6200ee',
  },
  transcriptText: {
    fontSize: 14,
    color: '#333',
    marginTop: 5,
  },
  recentSearchesContainer: {
    margin: 15,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  recentSearchesScroll: {
    flexDirection: 'row',
  },
  recentSearchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 20,
    marginRight: 10,
  },
  recentSearchIcon: {
    fontSize: 16,
    marginRight: 5,
  },
  recentSearchText: {
    fontSize: 14,
    color: '#333',
  },
  featuredContainer: {
    flex: 1,
    margin: 15,
    marginTop: 0,
  },
  productList: {
    paddingBottom: 100, // Extra padding at bottom for voice button
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    color: '#666',
    fontSize: 16,
  },
  noProductsText: {
    padding: 20,
    color: '#666',
    fontSize: 16,
    textAlign: 'center',
  },
  voiceButtonContainer: {
    position: 'absolute',
    bottom: 20,
    alignSelf: 'center',
  },
});

export default HomeScreen; 