{"version": 3, "file": "ExpoWebSpeechRecognition.web.js", "sourceRoot": "", "sources": ["../src/ExpoWebSpeechRecognition.web.ts"], "names": [], "mappings": "AAAA,IAAI,wBAAwB,GAAoC,IAAI,CAAC;AACrE,IAAI,wBAAwB,GAAoC,IAAI,CAAC;AACrE,IAAI,6BAA6B,GAAyC,IAAI,CAAC;AAE/E,IAAI,OAAO,uBAAuB,KAAK,WAAW,EAAE,CAAC;IACnD,wBAAwB,GAAG,uBAAuB,CAAC;IACnD,wBAAwB;QACtB,OAAO,uBAAuB,KAAK,WAAW;YAC5C,CAAC,CAAC,uBAAuB;YACzB,CAAC,CAAC,IAAI,CAAC;IACX,6BAA6B;QAC3B,OAAO,4BAA4B,KAAK,WAAW;YACjD,CAAC,CAAC,4BAA4B;YAC9B,CAAC,CAAC,IAAI,CAAC;AACb,CAAC;KAAM,IAAI,OAAO,iBAAiB,KAAK,WAAW,EAAE,CAAC;IACpD,wBAAwB,GAAG,iBAAiB,CAAC;IAC7C,wBAAwB;QACtB,OAAO,iBAAiB,KAAK,WAAW,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC;IACtE,6BAA6B;QAC3B,OAAO,sBAAsB,KAAK,WAAW;YAC3C,CAAC,CAAC,sBAAsB;YACxB,CAAC,CAAC,IAAI,CAAC;AACb,CAAC;AAED,MAAM,CAAC,MAAM,wBAAwB,GAAG,wBAAwB,CAAC;AACjE,MAAM,CAAC,MAAM,wBAAwB,GAAG,wBAAwB,CAAC;AACjE,MAAM,CAAC,MAAM,6BAA6B,GAAG,6BAA6B,CAAC", "sourcesContent": ["let browserSpeechRecognition: typeof SpeechRecognition | null = null;\nlet browserSpeechGrammarList: typeof SpeechGrammarList | null = null;\nlet browserSpeechRecognitionEvent: typeof SpeechRecognitionEvent | null = null;\n\nif (typeof webkitSpeechRecognition !== \"undefined\") {\n  browserSpeechRecognition = webkitSpeechRecognition;\n  browserSpeechGrammarList =\n    typeof webkitSpeechGrammarList !== \"undefined\"\n      ? webkitSpeechGrammarList\n      : null;\n  browserSpeechRecognitionEvent =\n    typeof webkitSpeechRecognitionEvent !== \"undefined\"\n      ? webkitSpeechRecognitionEvent\n      : null;\n} else if (typeof SpeechRecognition !== \"undefined\") {\n  browserSpeechRecognition = SpeechRecognition;\n  browserSpeechGrammarList =\n    typeof SpeechGrammarList !== \"undefined\" ? SpeechGrammarList : null;\n  browserSpeechRecognitionEvent =\n    typeof SpeechRecognitionEvent !== \"undefined\"\n      ? SpeechRecognitionEvent\n      : null;\n}\n\nexport const ExpoWebSpeechRecognition = browserSpeechRecognition;\nexport const ExpoWebSpeechGrammarList = browserSpeechGrammarList;\nexport const ExpoWebSpeechRecognitionEvent = browserSpeechRecognitionEvent;\n"]}