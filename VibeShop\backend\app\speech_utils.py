import speech_recognition as sr
from pydub import AudioSegment
import os
import logging

# Configure logging
logger = logging.getLogger(__name__)

def convert_audio_format(input_path, output_format="wav"):
    """
    Convert audio file to the specified format.
    
    Args:
        input_path (str): Path to input audio file
        output_format (str): Desired output format (default: wav)
        
    Returns:
        str: Path to converted audio file
    """
    try:
        # Get directory and filename
        directory = os.path.dirname(input_path)
        filename = os.path.basename(input_path)
        name_without_ext = os.path.splitext(filename)[0]
        
        # Set output path
        output_path = os.path.join(directory, f"{name_without_ext}.{output_format}")
        
        # Convert audio
        audio = AudioSegment.from_file(input_path)
        audio.export(output_path, format=output_format)
        
        logger.info(f"Converted {input_path} to {output_path}")
        return output_path
    
    except Exception as e:
        logger.error(f"Error converting audio: {e}")
        raise

def recognize_speech_from_file(file_path, language="en-US"):
    """
    Recognize speech from an audio file.
    
    Args:
        file_path (str): Path to audio file
        language (str): Language code for recognition
        
    Returns:
        str: Recognized text
    """
    recognizer = sr.Recognizer()
    
    # Adjust for ambient noise and improve recognition
    recognizer.dynamic_energy_threshold = True
    recognizer.energy_threshold = 4000
    
    try:
        with sr.AudioFile(file_path) as source:
            # Record audio from the file
            audio_data = recognizer.record(source)
            
            # Recognize speech using Google Speech Recognition
            text = recognizer.recognize_google(audio_data, language=language)
            return text
    
    except sr.UnknownValueError:
        logger.error("Speech Recognition could not understand audio")
        raise
    
    except sr.RequestError as e:
        logger.error(f"Could not request results from Speech Recognition service; {e}")
        raise
    
    except Exception as e:
        logger.error(f"Error recognizing speech: {e}")
        raise

def preprocess_command(command):
    """
    Preprocess the recognized command for better matching.
    
    Args:
        command (str): Raw recognized command
        
    Returns:
        str: Processed command
    """
    # Convert to lowercase
    command = command.lower()
    
    # Remove filler words
    fillers = ["um", "uh", "like", "so", "you know", "actually", "basically", "literally"]
    for filler in fillers:
        command = command.replace(f" {filler} ", " ")
    
    # Clean up common speech recognition errors
    command = command.replace("search 4", "search for")
    command = command.replace("search four", "search for")
    command = command.replace("such for", "search for")
    command = command.replace("search phone", "search for")
    
    # Standardize command formats
    if "search" in command and "for" not in command:
        parts = command.split("search")
        if len(parts) > 1:
            command = f"search for{parts[1]}"
    
    return command.strip() 