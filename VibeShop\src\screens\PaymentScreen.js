import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Alert,
  ActivityIndicator
} from 'react-native';
import { usePaystack } from 'react-native-paystack-webview';
import paymentService from '../services/paymentService';
import { useVoice } from '../context/VoiceContext';

const PaymentScreen = ({ route, navigation }) => {
  const { paymentInfo, order, userInfo } = route.params || {};
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const { speakFeedback } = useVoice();
  const { popup } = usePaystack();

  useEffect(() => {
    if (paymentInfo) {
      setLoading(false);
      if (!processing) {
        initiatePayment();
      }
    }
  }, [paymentInfo]);

  // Initiate payment using Paystack popup
  const initiatePayment = () => {
    if (!paymentInfo) return;

    popup.checkout({
      email: paymentInfo.paymentData.email,
      amount: paymentInfo.paymentData.amount / 100, // Divide by 100 since Paystack will multiply by 100 again
      reference: paymentInfo.paymentData.reference,
      currency: "GHS",
      channels: ["card", "bank", "mobile_money"],
      metadata: {
        custom_fields: [
          {
            display_name: "Order ID",
            variable_name: "order_id",
            value: order.orderId
          },
          {
            display_name: "Customer Name",
            variable_name: "customer_name",
            value: userInfo?.name || "Customer"
          }
        ]
      },
      onSuccess: handlePaymentSuccess,
      onCancel: handlePaymentCancel,
      onError: handlePaymentError
    });
  };

  // Handle payment response
  const handlePaymentSuccess = async (response) => {
    setProcessing(true);
    
    try {
      // Extract reference from response
      const reference = response?.data?.transactionRef?.reference || paymentInfo?.paymentData?.reference;
      
      // Verify payment with Paystack
      const verificationResult = await paymentService.verifyPayment(reference);
      
      if (verificationResult.status) {
        // Update order status
        await paymentService.updateOrderAfterPayment(order.orderId, verificationResult.data);
        
        // Provide voice feedback
        speakFeedback('Payment successful! Your order has been placed.');
        
        // Navigate to order confirmation
        navigation.replace('OrderConfirmation', {
          order,
          paymentDetails: verificationResult.data
        });
      } else {
        Alert.alert('Payment Failed', 'Your payment could not be verified. Please try again.');
      }
    } catch (error) {
      console.error('Payment verification error:', error);
      Alert.alert('Verification Error', 'There was an error verifying your payment.');
    } finally {
      setProcessing(false);
    }
  };
  
  const handlePaymentCancel = () => {
    Alert.alert(
      'Payment Cancelled',
      'Your payment was cancelled. Would you like to try again?',
      [
        {
          text: 'Try Again',
          onPress: initiatePayment
        },
        {
          text: 'Go Back',
          onPress: () => navigation.goBack(),
          style: 'cancel'
        }
      ]
    );
  };
  
  const handlePaymentError = (error) => {
    console.error('Payment error:', error);
    Alert.alert(
      'Payment Failed',
      'There was an error processing your payment. Please try again.',
      [
        {
          text: 'Try Again',
          onPress: initiatePayment
        },
        {
          text: 'Go Back',
          onPress: () => navigation.goBack(),
          style: 'cancel'
        }
      ]
    );
  };

  // Handle back navigation
  const handleBackPress = () => {
    if (processing) {
      Alert.alert('Payment in Progress', 'Please wait while your payment is being processed.');
      return;
    }
    
    Alert.alert(
      'Cancel Payment',
      'Are you sure you want to cancel this payment?',
      [
        {
          text: 'Continue Payment',
          style: 'cancel'
        },
        {
          text: 'Cancel Payment',
          onPress: () => navigation.goBack()
        }
      ]
    );
  };

  // For demo purposes, we'll provide a simulated payment button
  // In a real app, this would be handled by the WebView
  const handleSimulatePayment = async () => {
    setProcessing(true);
    
    // Simulate payment processing delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Create mock verification result
    const mockVerificationResult = {
      status: true,
      data: {
        reference: paymentInfo?.paymentData?.reference || `REF-${Date.now()}`,
        status: 'success',
        amount: paymentInfo?.paymentData?.amount || 10000,
        currency: 'GHS',
        transaction_date: new Date().toISOString(),
        gateway_response: 'Successful'
      }
    };
    
    try {
      // Update order status
      await paymentService.updateOrderAfterPayment(order.orderId, mockVerificationResult.data);
      
      // Provide voice feedback
      speakFeedback('Payment successful! Your order has been placed.');
      
      // Navigate to order confirmation
      navigation.replace('OrderConfirmation', {
        order,
        paymentDetails: mockVerificationResult.data
      });
    } catch (error) {
      console.error('Error updating order:', error);
      Alert.alert('Error', 'There was an error processing your payment.');
    } finally {
      setProcessing(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar backgroundColor="#6200ee" barStyle="light-content" />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerText}>Payment</Text>
      </View>
      
      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6200ee" />
          <Text style={styles.loadingText}>Loading payment gateway...</Text>
        </View>
      ) : processing ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6200ee" />
          <Text style={styles.loadingText}>Processing your payment...</Text>
          <Text style={styles.processingText}>Please do not close this screen</Text>
        </View>
      ) : (
        <View style={styles.contentContainer}>
          {/* Payment Summary */}
          <View style={styles.summaryContainer}>
            <Text style={styles.summaryTitle}>Payment Summary</Text>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Amount:</Text>
              <Text style={styles.summaryValue}>
                GHS {((paymentInfo?.paymentData?.amount || 0) / 100).toFixed(2)}
              </Text>
            </View>
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Reference:</Text>
              <Text style={styles.summaryValue}>
                {paymentInfo?.paymentData?.reference || 'N/A'}
              </Text>
            </View>
          </View>
          
          {/* Payment Buttons */}
          <TouchableOpacity 
            style={styles.payButton}
            onPress={initiatePayment}
          >
            <Text style={styles.payButtonText}>Pay with Paystack</Text>
          </TouchableOpacity>
          
          <Text style={styles.orText}>OR</Text>
          
          {/* Simulated Payment Options - For Demo Purposes */}
          <View style={styles.paymentOptionsContainer}>
            <Text style={styles.paymentOptionsTitle}>Simulate Payment (Demo Only)</Text>
            
            <TouchableOpacity 
              style={styles.paymentOption}
              onPress={handleSimulatePayment}
            >
              <Text style={styles.paymentOptionText}>Simulate Successful Payment</Text>
            </TouchableOpacity>
          </View>
          
          <Text style={styles.disclaimerText}>
            This app uses Paystack test mode. No real charges will be made.
            Use test card: 4084 0840 8408 4081, CVV: 408, Any future date, PIN: 0000, OTP: 123456
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#6200ee',
  },
  backButton: {
    marginRight: 10,
  },
  backButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#333',
    marginTop: 16,
  },
  processingText: {
    fontSize: 14,
    color: '#666',
    marginTop: 8,
    fontStyle: 'italic',
  },
  contentContainer: {
    flex: 1,
    padding: 16,
  },
  summaryContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    color: '#666',
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  payButton: {
    backgroundColor: '#6200ee',
    borderRadius: 5,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  payButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  orText: {
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    color: '#666',
    marginVertical: 16,
  },
  paymentOptionsContainer: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  paymentOptionsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  paymentOption: {
    backgroundColor: '#f5f5f5',
    borderRadius: 5,
    padding: 16,
    marginBottom: 8,
  },
  paymentOptionText: {
    fontSize: 16,
    color: '#333',
  },
  disclaimerText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 16,
  },
});

export default PaymentScreen; 