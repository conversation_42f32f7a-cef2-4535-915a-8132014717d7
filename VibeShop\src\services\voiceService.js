import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import * as Speech from 'expo-speech';
import { determineAction } from '../utils/commandProcessor';
import { Platform } from 'react-native';

// Voice recording settings
const RECORDING_OPTIONS = {
  android: {
    extension: '.m4a',
    outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
    audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
    sampleRate: 44100,
    numberOfChannels: 2,
    bitRate: 128000,
  },
  ios: {
    extension: '.m4a',
    outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
    audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
    sampleRate: 44100,
    numberOfChannels: 2,
    bitRate: 128000,
    linearPCMBitDepth: 16,
    linearPCMIsBigEndian: false,
    linearPCMIsFloat: false,
  },
};

// Basic shopping commands that we want to recognize
const SHOPPING_COMMANDS = [
  "search for shoes",
  "search for shirts",
  "search for pants",
  "add to cart",
  "view cart",
  "checkout",
  "confirm payment",
  "cancel",
  "help",
  "select item one",
  "select item two",
  "select item three",
  "select first item",
  "select second item",
  "select third item",
  "remove from cart",
  "clear cart",
  "go back",
  "go home"
];

// Import Speech Recognition if available
let Voice;
try {
  Voice = require('@react-native-voice/voice').default;
} catch (e) {
  console.log('Voice recognition module not available');
}

class VoiceService {
  constructor() {
    this.recording = null;
    this.sound = null;
    this.isRecording = false;
    this.isSpeaking = false;
    this.lastRecognizedCommand = null;
    this.voiceRecognition = null;
    this.recognitionResult = null;
    
    // Initialize speech recognition if available
    this.initSpeechRecognition();
  }

  // Initialize speech recognition
  async initSpeechRecognition() {
    try {
      if (Voice) {
        // Initialize Voice
        Voice.onSpeechStart = this.onSpeechStart.bind(this);
        Voice.onSpeechEnd = this.onSpeechEnd.bind(this);
        Voice.onSpeechResults = this.onSpeechResults.bind(this);
        Voice.onSpeechError = this.onSpeechError.bind(this);
        
        console.log('Speech recognition initialized');
        this.voiceRecognition = Voice;
      } else {
        console.log('Speech recognition not available, using audio analysis fallback');
      }
    } catch (error) {
      console.error('Failed to initialize speech recognition:', error);
    }
  }
  
  // Speech recognition event handlers
  onSpeechStart() {
    console.log('Speech recognition started');
  }
  
  onSpeechEnd() {
    console.log('Speech recognition ended');
  }
  
  onSpeechResults(event) {
    if (event && event.value && event.value.length > 0) {
      this.recognitionResult = event.value[0];
      console.log('Speech recognition result:', this.recognitionResult);
    }
  }
  
  onSpeechError(error) {
    console.log('Speech recognition error:', error);
  }

  // Initialize audio recording
  async init() {
    try {
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false,
      });
    } catch (error) {
      console.error('Failed to initialize audio:', error);
      throw error;
    }
  }

  // Start recording voice
  async startRecording() {
    try {
      if (this.isRecording) {
        console.log('Already recording');
        return;
      }

      // Reset recognition result
      this.recognitionResult = null;
      
      // Try to use Voice recognition if available
      if (this.voiceRecognition) {
        try {
          await this.voiceRecognition.start('en-US');
          console.log('Voice recognition started');
        } catch (e) {
          console.error('Error starting voice recognition:', e);
        }
      }

      // Also start audio recording as backup
      await this.init();
      this.recording = new Audio.Recording();
      await this.recording.prepareToRecordAsync(RECORDING_OPTIONS);
      await this.recording.startAsync();
      this.isRecording = true;
      console.log('Recording started');
    } catch (error) {
      console.error('Failed to start recording:', error);
      throw error;
    }
  }

  // Stop recording and return the audio file URI
  async stopRecording() {
    try {
      if (!this.isRecording) {
        console.log('Not recording');
        return null;
      }

      // Stop Voice recognition if available
      if (this.voiceRecognition) {
        try {
          await this.voiceRecognition.stop();
          console.log('Voice recognition stopped');
        } catch (e) {
          console.error('Error stopping voice recognition:', e);
        }
      }

      // Also stop audio recording
      await this.recording.stopAndUnloadAsync();
      const uri = this.recording.getURI();
      this.isRecording = false;
      this.recording = null;
      console.log('Recording stopped, file URI:', uri);
      return uri;
    } catch (error) {
      console.error('Failed to stop recording:', error);
      throw error;
    }
  }

  // Process the recorded audio with on-device recognition
  async processAudio(audioUri) {
    try {
      console.log('Processing audio file:', audioUri);
      
      // If we have a result from Voice recognition, use that
      if (this.recognitionResult) {
        const recognizedText = this.recognitionResult.toLowerCase().trim();
        console.log('Using Voice recognition result:', recognizedText);
        
        // Find the closest matching command
        const matchedCommand = this.findClosestCommand(recognizedText);
        console.log('Matched command:', matchedCommand);
        
        return {
          text: matchedCommand || recognizedText,
          source: 'voice-recognition'
        };
      }
      
      // If no Voice recognition result, use audio analysis as fallback
      console.log('No Voice recognition result, using audio analysis fallback');
      return this.processAudioFallback(audioUri);
    } catch (error) {
      console.error('Failed to process audio:', error);
      throw error;
    }
  }
  
  // Find the closest matching command from our command list
  findClosestCommand(text) {
    if (!text) return null;
    
    // First, check for exact matches
    const exactMatch = SHOPPING_COMMANDS.find(cmd => 
      cmd.toLowerCase() === text.toLowerCase()
    );
    
    if (exactMatch) return exactMatch;
    
    // If no exact match, look for the command with most matching words
    const textWords = text.toLowerCase().split(/\s+/);
    
    let bestMatch = null;
    let bestScore = 0;
    
    for (const command of SHOPPING_COMMANDS) {
      const commandWords = command.toLowerCase().split(/\s+/);
      let score = 0;
      
      // Count matching words
      for (const word of textWords) {
        if (commandWords.includes(word)) {
          score++;
        }
      }
      
      // Bonus for commands that start with the same words
      if (command.toLowerCase().startsWith(textWords[0])) {
        score += 0.5;
      }
      
      // Update best match if this score is higher
      if (score > bestScore) {
        bestMatch = command;
        bestScore = score;
      }
    }
    
    // Only return a match if the score is above a threshold
    return bestScore >= 1 ? bestMatch : null;
  }
  
  // Process audio using fallback method when speech recognition fails
  async processAudioFallback(audioUri) {
    try {
      // Extract audio features
      const audioFeatures = await this.extractAudioFeatures(audioUri);
      
      // Determine the most likely command based on audio characteristics
      const command = this.determineCommandFromAudio(audioFeatures);
      
      console.log('Audio analysis fallback result:', command);
      
      return {
        text: command,
        source: 'audio-analysis'
      };
    } catch (error) {
      console.error('Failed audio fallback processing:', error);
      throw error;
    }
  }
  
  // Extract basic audio features
  async extractAudioFeatures(audioUri) {
    try {
      const fileInfo = await FileSystem.getInfoAsync(audioUri);
      
      // Calculate a simple energy value based on file size
      const energy = Math.min(fileInfo.size / 100000, 1.0);
      const duration = fileInfo.size / 16000; // Rough estimate
      
      return {
        energy,
        duration,
        size: fileInfo.size
      };
    } catch (error) {
      console.error('Failed to extract audio features:', error);
      return { energy: 0.5, duration: 2.0, size: 0 };
    }
  }
  
  // Determine command from audio features (fallback method)
  determineCommandFromAudio(features) {
    // Use a combination of energy and duration to guess the command
    // This is a very simple heuristic and won't be very accurate
    
    // Use the last recognized command as context if available
    const lastCommand = this.lastRecognizedCommand;
    
    if (features.energy > 0.7) {
      // High energy usually means search commands
      if (features.duration > 2.0) {
        return "search for pants";
      } else if (features.duration > 1.5) {
        return "search for shirts";
      } else {
        return "search for shoes";
      }
    } else if (features.energy > 0.5) {
      // Medium-high energy for cart operations
      if (lastCommand && lastCommand.includes("search")) {
        return "select item one";
      } else if (lastCommand && lastCommand.includes("select")) {
        return "add to cart";
      } else {
        return "view cart";
      }
    } else if (features.energy > 0.3) {
      // Medium energy for checkout operations
      if (lastCommand && lastCommand.includes("cart")) {
        return "checkout";
      } else if (lastCommand && lastCommand.includes("checkout")) {
        return "confirm payment";
      } else {
        return "help";
      }
    } else {
      // Low energy for simple commands
      if (lastCommand) {
        return "cancel";
      } else {
        return "help";
      }
    }
  }

  // Speak text using text-to-speech
  async speak(text) {
    try {
      if (this.isSpeaking) {
        await Speech.stop();
      }
      
      this.isSpeaking = true;
      await Speech.speak(text, {
        language: 'en-US',
        pitch: 1.0,
        rate: 0.9,
        onDone: () => {
          this.isSpeaking = false;
        },
        onError: (error) => {
          console.error('TTS error:', error);
          this.isSpeaking = false;
        }
      });
    } catch (error) {
      console.error('Failed to speak:', error);
      this.isSpeaking = false;
      throw error;
    }
  }
  
  // Process voice command and return action
  async processCommand(command) {
    if (!command) {
      return {
        action: 'unknown',
        payload: null
      };
    }
    
    // Store this command for context in future recognitions
    this.lastRecognizedCommand = command;
    
    // Use the command processor to determine the action
    const result = determineAction(command);
    return result;
  }
}

export default new VoiceService(); 
