import { Audio } from "expo-av";
import * as Speech from "expo-speech";
import { Platform } from "react-native";
import { determineAction } from "../utils/commandProcessor";

// Voice recording settings optimized for speech recognition
const RECORDING_OPTIONS = {
  android: {
    extension: ".m4a",
    outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
    audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
    sampleRate: 16000, // Optimized for speech recognition
    numberOfChannels: 1, // Mono for better speech recognition
    bitRate: 64000, // Lower bitrate for speech
  },
  ios: {
    extension: ".m4a",
    outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
    audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_MEDIUM,
    sampleRate: 16000, // Optimized for speech recognition
    numberOfChannels: 1, // Mono for better speech recognition
    bitRate: 64000, // Lower bitrate for speech
    linearPCMBitDepth: 16,
    linearPCMIsBigEndian: false,
    linearPCMIsFloat: false,
  },
};

// Enhanced shopping commands with variations
const SHOPPING_COMMANDS = [
  // Search commands
  "search for shoes",
  "find shoes",
  "show me shoes",
  "look for shoes",
  "search for shirts",
  "find shirts",
  "show me shirts",
  "look for shirts",
  "search for pants",
  "find pants",
  "show me pants",
  "look for pants",
  "search for dress",
  "find dress",
  "show me dress",
  "look for dress",
  "search for jacket",
  "find jacket",
  "show me jacket",
  "look for jacket",
  "search for sneakers",
  "find sneakers",
  "show me sneakers",
  "search for boots",
  "find boots",
  "show me boots",

  // Cart commands
  "add to cart",
  "add item",
  "add this",
  "put in cart",
  "view cart",
  "show cart",
  "open cart",
  "my cart",
  "remove from cart",
  "delete item",
  "remove this",
  "clear cart",
  "empty cart",
  "delete all",

  // Navigation commands
  "checkout",
  "proceed to checkout",
  "buy now",
  "purchase",
  "confirm payment",
  "pay now",
  "complete purchase",
  "finish order",
  "go back",
  "back",
  "previous",
  "return",
  "go home",
  "home",
  "main page",
  "start over",

  // Selection commands
  "select item one",
  "choose first",
  "pick first",
  "first item",
  "select item two",
  "choose second",
  "pick second",
  "second item",
  "select item three",
  "choose third",
  "pick third",
  "third item",

  // Utility commands
  "help",
  "what can I say",
  "commands",
  "instructions",
  "cancel",
  "stop",
  "never mind",
  "abort",
];

// Import Speech Recognition - using @react-native-voice/voice with fallback
let Voice;
let isVoiceModuleAvailable = false;

try {
  Voice = require("@react-native-voice/voice").default;
  isVoiceModuleAvailable = true;
  console.log("Voice recognition module loaded successfully");
} catch (e) {
  console.log(
    "Voice recognition module not available, using audio recording with pattern matching"
  );
  Voice = null;
  isVoiceModuleAvailable = false;
}

class VoiceService {
  constructor() {
    this.recording = null;
    this.isRecording = false;
    this.isSpeaking = false;
    this.lastRecognizedCommand = null;
    this.recognitionResult = null;
    this.isVoiceAvailable = false;
    this.recognitionTimeout = null;
    this.isListening = false;

    // Initialize speech recognition if available
    this.initSpeechRecognition();
  }

  // Initialize speech recognition
  async initSpeechRecognition() {
    try {
      if (isVoiceModuleAvailable && Voice) {
        // Set up event handlers
        Voice.onSpeechStart = this.onSpeechStart.bind(this);
        Voice.onSpeechEnd = this.onSpeechEnd.bind(this);
        Voice.onSpeechResults = this.onSpeechResults.bind(this);
        Voice.onSpeechError = this.onSpeechError.bind(this);
        Voice.onSpeechPartialResults = this.onSpeechPartialResults.bind(this);

        // Check if speech recognition is available on this device
        try {
          const isAvailable = await Voice.isAvailable();
          this.isVoiceAvailable = isAvailable;
          console.log(
            "Speech recognition initialized, available:",
            isAvailable
          );
        } catch (availabilityError) {
          console.log(
            "Voice.isAvailable() failed, assuming not available:",
            availabilityError
          );
          this.isVoiceAvailable = false;
        }
      } else {
        console.log(
          "Speech recognition module not loaded, using audio recording with pattern matching"
        );
        this.isVoiceAvailable = false;
      }
    } catch (error) {
      console.error("Failed to initialize speech recognition:", error);
      this.isVoiceAvailable = false;
    }
  }

  // Speech recognition event handlers
  onSpeechStart() {
    console.log("Speech recognition started");
    this.recognitionResult = null;
    this.isListening = true;
  }

  onSpeechEnd() {
    console.log("Speech recognition ended");
    this.isListening = false;
  }

  onSpeechResults(event) {
    if (event && event.value && event.value.length > 0) {
      this.recognitionResult = event.value[0];
      console.log("Speech recognition result:", this.recognitionResult);

      // Clear timeout since we got a result
      if (this.recognitionTimeout) {
        clearTimeout(this.recognitionTimeout);
        this.recognitionTimeout = null;
      }
    }
  }

  onSpeechPartialResults(event) {
    if (event && event.value && event.value.length > 0) {
      console.log("Partial speech result:", event.value[0]);
    }
  }

  onSpeechError(error) {
    console.log("Speech recognition error:", error);
    this.recognitionResult = null;
    this.isListening = false;

    // Clear timeout on error
    if (this.recognitionTimeout) {
      clearTimeout(this.recognitionTimeout);
      this.recognitionTimeout = null;
    }
  }

  // Initialize audio recording
  async init() {
    try {
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false,
      });
    } catch (error) {
      console.error("Failed to initialize audio:", error);
      throw error;
    }
  }

  // Start recording voice
  async startRecording() {
    try {
      if (this.isRecording) {
        console.log("Already recording");
        return;
      }

      // Reset recognition result
      this.recognitionResult = null;
      this.isRecording = true;

      // Try to use Voice recognition if available
      if (this.isVoiceAvailable && Voice) {
        try {
          await Voice.start("en-US");
          console.log("Voice recognition started");

          // Set a timeout for recognition (10 seconds)
          this.recognitionTimeout = setTimeout(() => {
            console.log("Voice recognition timeout");
            this.stopRecording();
          }, 10000);

          return; // Don't use audio recording if voice recognition works
        } catch (e) {
          console.error("Error starting voice recognition:", e);
        }
      }

      // Fallback to audio recording if voice recognition is not available
      console.log("Using audio recording fallback");
      await this.init();
      this.recording = new Audio.Recording();
      await this.recording.prepareToRecordAsync(RECORDING_OPTIONS);
      await this.recording.startAsync();
      console.log("Audio recording started");
    } catch (error) {
      console.error("Failed to start recording:", error);
      this.isRecording = false;
      throw error;
    }
  }

  // Stop recording and return the audio file URI
  async stopRecording() {
    try {
      if (!this.isRecording) {
        console.log("Not recording");
        return null;
      }

      this.isRecording = false;

      // Clear recognition timeout
      if (this.recognitionTimeout) {
        clearTimeout(this.recognitionTimeout);
        this.recognitionTimeout = null;
      }

      // Stop Voice recognition if available
      if (this.isVoiceAvailable && Voice) {
        try {
          await Voice.stop();
          console.log("Voice recognition stopped");

          // Wait a bit for the result to come in
          await new Promise((resolve) => setTimeout(resolve, 500));

          // Return a dummy URI since we're using voice recognition
          return "voice_recognition_result";
        } catch (e) {
          console.error("Error stopping voice recognition:", e);
        }
      }

      // Stop audio recording if it was used
      if (this.recording) {
        await this.recording.stopAndUnloadAsync();
        const uri = this.recording.getURI();
        this.recording = null;
        console.log("Audio recording stopped, file URI:", uri);
        return uri;
      }

      return null;
    } catch (error) {
      console.error("Failed to stop recording:", error);
      this.isRecording = false;
      throw error;
    }
  }

  // Process the recorded audio with on-device recognition
  async processAudio(audioUri) {
    try {
      console.log("Processing audio:", audioUri);

      // If we have a result from Voice recognition, use that
      if (this.recognitionResult) {
        const recognizedText = this.recognitionResult.toLowerCase().trim();
        console.log("Using Voice recognition result:", recognizedText);

        // Find the closest matching command or extract meaningful content
        const processedCommand = this.processRecognizedText(recognizedText);
        console.log("Processed command:", processedCommand);

        return {
          text: processedCommand,
          source: "voice-recognition",
        };
      }

      // If no Voice recognition result and we have an audio file, use fallback
      if (audioUri && audioUri !== "voice_recognition_result") {
        console.log(
          "No Voice recognition result, using audio analysis fallback"
        );
        return this.processAudioFallback(audioUri);
      }

      // No result available
      throw new Error("No speech recognized");
    } catch (error) {
      console.error("Failed to process audio:", error);
      throw error;
    }
  }

  // Process recognized text to extract meaningful commands
  processRecognizedText(text) {
    if (!text) return null;

    // First, try to find exact command matches
    const exactMatch = this.findClosestCommand(text);
    if (exactMatch) return exactMatch;

    // If no exact match, return the original text for further processing
    // This allows for more flexible command processing in the command processor
    return text;
  }

  // Find the closest matching command from our command list
  findClosestCommand(text) {
    if (!text) return null;

    // First, check for exact matches
    const exactMatch = SHOPPING_COMMANDS.find(
      (cmd) => cmd.toLowerCase() === text.toLowerCase()
    );

    if (exactMatch) return exactMatch;

    // If no exact match, look for the command with most matching words
    const textWords = text.toLowerCase().split(/\s+/);

    let bestMatch = null;
    let bestScore = 0;

    for (const command of SHOPPING_COMMANDS) {
      const commandWords = command.toLowerCase().split(/\s+/);
      let score = 0;

      // Count matching words
      for (const word of textWords) {
        if (commandWords.includes(word)) {
          score++;
        }
      }

      // Bonus for commands that start with the same words
      if (command.toLowerCase().startsWith(textWords[0])) {
        score += 0.5;
      }

      // Update best match if this score is higher
      if (score > bestScore) {
        bestMatch = command;
        bestScore = score;
      }
    }

    // Only return a match if the score is above a threshold
    return bestScore >= 1 ? bestMatch : null;
  }

  // Process audio using intelligent fallback method when speech recognition fails
  async processAudioFallback(audioUri) {
    try {
      console.log("Processing audio with intelligent fallback method");

      // Extract basic audio features to determine if there was actually speech
      const audioFeatures = await this.extractAudioFeatures(audioUri);

      if (audioFeatures.energy < 0.1 || audioFeatures.duration < 0.5) {
        // Very low energy or short duration - likely no speech
        throw new Error(
          "No speech detected. Please try again and speak clearly."
        );
      }

      // Use context-aware command suggestion
      const command = this.getSuggestedCommand(audioFeatures);

      console.log("Intelligent fallback result:", command);

      return {
        text: command,
        source: "intelligent-fallback",
        confidence: 0.6, // Lower confidence for fallback
      };
    } catch (error) {
      console.error("Failed audio fallback processing:", error);
      throw error;
    }
  }

  // Extract basic audio features
  async extractAudioFeatures(audioUri) {
    try {
      const fileInfo = await FileSystem.getInfoAsync(audioUri);

      // Calculate a simple energy value based on file size
      const energy = Math.min(fileInfo.size / 100000, 1.0);
      const duration = fileInfo.size / 16000; // Rough estimate

      return {
        energy,
        duration,
        size: fileInfo.size,
      };
    } catch (error) {
      console.error("Failed to extract audio features:", error);
      return { energy: 0.5, duration: 2.0, size: 0 };
    }
  }

  // Determine command from audio features (fallback method)
  determineCommandFromAudio(features) {
    // Use a combination of energy and duration to guess the command
    // This is a very simple heuristic and won't be very accurate

    // Use the last recognized command as context if available
    const lastCommand = this.lastRecognizedCommand;

    if (features.energy > 0.7) {
      // High energy usually means search commands
      if (features.duration > 2.0) {
        return "search for pants";
      } else if (features.duration > 1.5) {
        return "search for shirts";
      } else {
        return "search for shoes";
      }
    } else if (features.energy > 0.5) {
      // Medium-high energy for cart operations
      if (lastCommand && lastCommand.includes("search")) {
        return "select item one";
      } else if (lastCommand && lastCommand.includes("select")) {
        return "add to cart";
      } else {
        return "view cart";
      }
    } else if (features.energy > 0.3) {
      // Medium energy for checkout operations
      if (lastCommand && lastCommand.includes("cart")) {
        return "checkout";
      } else if (lastCommand && lastCommand.includes("checkout")) {
        return "confirm payment";
      } else {
        return "help";
      }
    } else {
      // Low energy for simple commands
      if (lastCommand) {
        return "cancel";
      } else {
        return "help";
      }
    }
  }

  // Speak text using text-to-speech
  async speak(text) {
    try {
      if (this.isSpeaking) {
        await Speech.stop();
      }

      this.isSpeaking = true;
      await Speech.speak(text, {
        language: "en-US",
        pitch: 1.0,
        rate: 0.9,
        onDone: () => {
          this.isSpeaking = false;
        },
        onError: (error) => {
          console.error("TTS error:", error);
          this.isSpeaking = false;
        },
      });
    } catch (error) {
      console.error("Failed to speak:", error);
      this.isSpeaking = false;
      throw error;
    }
  }

  // Process voice command and return action
  async processCommand(command) {
    if (!command) {
      return {
        action: "unknown",
        payload: null,
      };
    }

    // Store this command for context in future recognitions
    this.lastRecognizedCommand = command;

    // Use the command processor to determine the action
    const result = determineAction(command);
    return result;
  }
}

export default new VoiceService();
