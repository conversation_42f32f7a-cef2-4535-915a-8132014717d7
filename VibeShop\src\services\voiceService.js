import { Audio } from 'expo-av';
import * as FileSystem from 'expo-file-system';
import * as Speech from 'expo-speech';
import { determineAction } from '../utils/commandProcessor';
import { Platform } from 'react-native';
import { API_CONFIG } from '../config/api';

// Voice recording settings
const RECORDING_OPTIONS = {
  android: {
    extension: '.m4a',
    outputFormat: Audio.RECORDING_OPTION_ANDROID_OUTPUT_FORMAT_MPEG_4,
    audioEncoder: Audio.RECORDING_OPTION_ANDROID_AUDIO_ENCODER_AAC,
    sampleRate: 44100,
    numberOfChannels: 2,
    bitRate: 128000,
  },
  ios: {
    extension: '.m4a',
    outputFormat: Audio.RECORDING_OPTION_IOS_OUTPUT_FORMAT_MPEG4AAC,
    audioQuality: Audio.RECORDING_OPTION_IOS_AUDIO_QUALITY_HIGH,
    sampleRate: 44100,
    numberOfChannels: 2,
    bitRate: 128000,
    linearPCMBitDepth: 16,
    linearPCMIsBigEndian: false,
    linearPCMIsFloat: false,
  },
};

class VoiceService {
  constructor() {
    this.recording = null;
    this.sound = null;
    this.isRecording = false;
    this.isSpeaking = false;
    this.serverAvailable = false;
    this.useAdvancedRecognition = false;
    this.availableModels = [];
    
    // Check if the server is available
    this.checkServerAvailability();
    this.fetchAvailableModels();
  }

  // Check if the Python server is available
  async checkServerAvailability() {
    try {
      const response = await fetch(`${API_CONFIG.baseUrl}${API_CONFIG.endpoints.health}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        // Add timeout to prevent hanging
        timeout: 5000
      });
      
      if (response.ok) {
        console.log('Speech recognition server is available');
        this.serverAvailable = true;
      } else {
        console.warn('Speech recognition server returned error status');
        this.serverAvailable = false;
        throw new Error('Speech recognition server is not available');
      }
    } catch (error) {
      console.warn('Speech recognition server is not available:', error);
      this.serverAvailable = false;
      throw new Error('Speech recognition server is not available');
    }
  }

  // Fetch available speech recognition models
  async fetchAvailableModels() {
    try {
      if (!this.serverAvailable) {
        await this.checkServerAvailability();
      }
      
      const response = await fetch(`${API_CONFIG.baseUrl}/models`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        // Add timeout to prevent hanging
        timeout: 5000
      });
      
      if (response.ok) {
        const data = await response.json();
        this.availableModels = data.models;
        console.log('Available speech recognition models:', this.availableModels);
        
        // Check if advanced recognition is available
        const advancedModel = this.availableModels.find(model => model.id === 'wav2vec2');
        if (advancedModel) {
          console.log('Advanced speech recognition is available');
          this.useAdvancedRecognition = true;
        }
      }
    } catch (error) {
      console.warn('Failed to fetch available models:', error);
    }
  }

  // Initialize audio recording
  async init() {
    try {
      await Audio.requestPermissionsAsync();
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: true,
        playThroughEarpieceAndroid: false,
        staysActiveInBackground: false,
      });
    } catch (error) {
      console.error('Failed to initialize audio:', error);
      throw error;
    }
  }

  // Start recording voice
  async startRecording() {
    try {
      // Check server availability before recording
      try {
        await this.checkServerAvailability();
      } catch (error) {
        console.error('Server not available, cannot start recording:', error);
        throw new Error('Speech recognition server is not available. Please check your connection and try again.');
      }
      
      if (this.isRecording) {
        console.log('Already recording');
        return;
      }

      await this.init();
      this.recording = new Audio.Recording();
      await this.recording.prepareToRecordAsync(RECORDING_OPTIONS);
      await this.recording.startAsync();
      this.isRecording = true;
      console.log('Recording started');
    } catch (error) {
      console.error('Failed to start recording:', error);
      throw error;
    }
  }

  // Stop recording and return the audio file URI
  async stopRecording() {
    try {
      if (!this.isRecording) {
        console.log('Not recording');
        return null;
      }

      await this.recording.stopAndUnloadAsync();
      const uri = this.recording.getURI();
      this.isRecording = false;
      this.recording = null;
      console.log('Recording stopped, file URI:', uri);
      return uri;
    } catch (error) {
      console.error('Failed to stop recording:', error);
      this.isRecording = false;
      this.recording = null;
      throw error;
    }
  }

  // Process the recorded audio with server-based recognition
  async processAudio(audioUri) {
    try {
      console.log('Processing audio file:', audioUri);
      
      // Ensure server is available
      if (!this.serverAvailable) {
        try {
          await this.checkServerAvailability();
        } catch (error) {
          console.error('Server not available for processing audio:', error);
          throw new Error('Speech recognition server is not available. Please check your connection and try again.');
        }
      }
      
      // Use the server for speech recognition
      const result = await this.recognizeWithServer(audioUri);
      console.log('Server recognized command:', result);
      return result;
    } catch (error) {
      console.error('Failed to process audio:', error);
      throw error;
    }
  }
  
  // Recognize speech using the Python backend
  async recognizeWithServer(audioUri) {
    try {
      // Create form data for the request
      const formData = new FormData();
      
      // Add the audio file to the form data
      formData.append('audio', {
        uri: audioUri,
        name: 'recording.m4a',
        type: 'audio/m4a'
      });
      
      // Add language parameter
      formData.append('language', 'en-US');
      
      // Add advanced recognition parameter if available
      if (this.useAdvancedRecognition) {
        formData.append('advanced', 'true');
        console.log('Using advanced speech recognition');
      }
      
      // Send the request to the server
      const response = await fetch(`${API_CONFIG.baseUrl}${API_CONFIG.endpoints.recognize}`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        // Add timeout to prevent hanging
        timeout: 30000 // Longer timeout for processing
      });
      
      // Parse the response
      const result = await response.json();
      
      if (result.success) {
        return {
          text: result.text,
          source: 'server',
          advanced: result.advanced || false
        };
      } else {
        throw new Error(result.error || 'Unknown server error');
      }
    } catch (error) {
      console.error('Server recognition error:', error);
      throw error;
    }
  }
  
  // Play audio file (for debugging)
  async playAudio(audioUri) {
    try {
      const { sound } = await Audio.Sound.createAsync({ uri: audioUri });
      this.sound = sound;
      await this.sound.playAsync();
    } catch (error) {
      console.error('Failed to play audio:', error);
    }
  }

  // Speak text using text-to-speech
  async speak(text) {
    try {
      if (this.isSpeaking) {
        await Speech.stop();
      }
      
      this.isSpeaking = true;
      await Speech.speak(text, {
        language: 'en-US',
        pitch: 1.0,
        rate: 0.9,
        onDone: () => {
          this.isSpeaking = false;
        },
        onError: (error) => {
          console.error('TTS error:', error);
          this.isSpeaking = false;
        }
      });
    } catch (error) {
      console.error('Failed to speak:', error);
      this.isSpeaking = false;
      throw error;
    }
  }
  
  // Process voice command and return action
  async processCommand(command) {
    if (!command) {
      return {
        action: 'unknown',
        payload: null
      };
    }
    
    // Use the command processor to determine the action
    const result = determineAction(command);
    return result;
  }
}

export default new VoiceService(); 
